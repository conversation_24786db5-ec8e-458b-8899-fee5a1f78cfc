<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Operation Homecoming - Advanced Story Builder</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: #fff;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: grid;
            grid-template-columns: var(--sidebar-width, 320px) 1fr var(--properties-width, 420px);
            grid-template-rows: 70px 1fr;
            height: 100vh;
            transition: grid-template-columns 0.3s ease;
        }

        .container.sidebar-collapsed {
            --sidebar-width: 0px;
        }

        .container.properties-collapsed {
            --properties-width: 0px;
        }

        .container.both-collapsed {
            --sidebar-width: 0px;
            --properties-width: 0px;
        }

        .header {
            grid-column: 1 / -1;
            background: rgba(0, 0, 0, 0.4);
            display: flex;
            align-items: center;
            padding: 0 20px;
            border-bottom: 3px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1000;
        }

        .header h1 {
            color: #fff;
            font-size: 26px;
            margin-right: auto;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header-buttons {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .toggle-buttons {
            display: flex;
            gap: 8px;
            margin-right: 15px;
        }

        .toggle-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 6px;
            color: #fff;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .toggle-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .toggle-btn.active {
            background: rgba(76, 175, 80, 0.6);
        }

        .autosave-indicator {
            display: flex;
            align-items: center;
            margin-right: 20px;
            font-size: 12px;
            color: #4CAF50;
        }

        .autosave-indicator.saving {
            color: #FF9800;
        }

        .user-status {
            display: flex;
            align-items: center;
            margin-right: 20px;
            font-size: 12px;
            color: #fff;
            background: rgba(0, 0, 0, 0.3);
            padding: 5px 10px;
            border-radius: 15px;
        }

        .lobby-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .lobby-user {
            display: flex;
            align-items: center;
            margin: 5px 0;
            padding: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .btn {
            padding: 10px 18px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }

        .sidebar {
            background: rgba(0, 0, 0, 0.5);
            padding: 20px;
            border-right: 3px solid rgba(255, 255, 255, 0.1);
            overflow-y: auto;
            transition: transform 0.3s ease, opacity 0.3s ease;
            position: relative;
        }

        .sidebar-section {
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
        }

        .sidebar-section h3 {
            margin-bottom: 15px;
            color: #fff;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 8px;
            font-size: 16px;
        }

        .node-type {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            background: rgba(255, 255, 255, 0.05);
            touch-action: manipulation;
        }

        .node-type:hover, .node-type:active {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateX(5px);
        }

        .node-color {
            width: 22px;
            height: 22px;
            border-radius: 50%;
            margin-right: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .canvas-container {
            position: relative;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.08) 1px, transparent 1px);
            background-size: 25px 25px;
            overflow: hidden;
            cursor: grab;
        }

        .canvas-container.panning {
            cursor: grabbing;
        }

        #storyCanvas {
            width: 100%;
            height: 100%;
            display: block;
            touch-action: none;
        }

        .properties-panel {
            background: rgba(0, 0, 0, 0.5);
            padding: 20px;
            border-left: 3px solid rgba(255, 255, 255, 0.1);
            overflow-y: auto;
            transition: transform 0.3s ease, opacity 0.3s ease;
            position: relative;
        }

        .properties-panel h3 {
            margin-bottom: 15px;
            color: #fff;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 8px;
            font-size: 18px;
        }

        .form-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .form-section h4 {
            color: #4CAF50;
            margin-bottom: 12px;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: bold;
            color: #ddd;
            font-size: 13px;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
            transition: border-color 0.3s ease;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            border-color: #4CAF50;
            outline: none;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .input-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .input-group input {
            flex: 1;
        }

        .probability-slider {
            width: 100%;
            margin: 10px 0;
        }

        .connections-list {
            max-height: 300px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 12px;
        }

        .connection-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin: 8px 0;
            border-left: 4px solid #2196F3;
        }

        .connection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .connection-label {
            background: rgba(33, 150, 243, 0.3);
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 8px;
        }

        .connection-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .connection-field {
            display: flex;
            flex-direction: column;
        }

        .connection-field label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 4px;
        }

        .connection-field input,
        .connection-field select {
            padding: 6px;
            font-size: 12px;
        }

        .skills-section {
            background: rgba(76, 175, 80, 0.1);
            border: 2px solid rgba(76, 175, 80, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .skills-section h4 {
            color: #4CAF50;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .skill-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 12px;
            margin: 8px 0;
            border-left: 3px solid #4CAF50;
        }

        .skill-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .skill-name {
            font-weight: bold;
            color: #4CAF50;
        }

        .skill-controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
        }

        .skill-input-group {
            display: flex;
            flex-direction: column;
        }

        .skill-input-group label {
            font-size: 11px;
            color: #ccc;
            margin-bottom: 3px;
        }

        .skill-input-group input {
            padding: 6px;
            font-size: 12px;
        }

        .competency-hint {
            font-size: 10px;
            color: #888;
            font-style: italic;
            margin-top: 2px;
        }

        .info-box {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid rgba(255, 193, 7, 0.4);
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            font-size: 12px;
            line-height: 1.4;
        }

        .info-box .icon {
            color: #FFC107;
            font-weight: bold;
            margin-right: 5px;
        }

        .story-stats {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 4px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-value {
            font-weight: bold;
            color: #4CAF50;
        }

        .path-estimation {
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
        }

        .path-estimation h5 {
            color: #FFC107;
            margin-bottom: 8px;
        }

        .export-section {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
        }

        .export-section h4 {
            margin-bottom: 12px;
            color: #4CAF50;
        }

        .json-output {
            background: rgba(0, 0, 0, 0.6);
            padding: 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 150px;
            overflow-y: auto;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .story-browser {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .story-list {
            max-height: 150px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            padding: 8px;
        }

        .story-item {
            padding: 8px;
            margin: 2px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 12px;
            touch-action: manipulation;
        }

        .story-item:hover, .story-item:active {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 10000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
        }

        .modal-content {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            margin: 2% auto;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }

        .modal-header h2 {
            color: #fff;
            margin: 0;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #fff;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding-top: 15px;
            border-top: 2px solid rgba(255, 255, 255, 0.2);
        }

        .requirements-builder {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 10px;
            margin-bottom: 15px;
        }

        .requirement-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 6px;
            margin: 5px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .metadata-builder {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: 10px;
            margin-bottom: 15px;
        }

        .faction-selector {
            background: rgba(156, 39, 176, 0.1);
            border: 2px solid rgba(156, 39, 176, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .faction-selector h4 {
            color: #9C27B0;
            margin-bottom: 12px;
        }

        .trait-selector {
            background: rgba(255, 152, 0, 0.1);
            border: 2px solid rgba(255, 152, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .trait-selector h4 {
            color: #FF9800;
            margin-bottom: 12px;
        }

        .trait-category {
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            padding: 10px;
        }

        .trait-category h5 {
            color: #FFB74D;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .trait-item {
            display: inline-block;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            touch-action: manipulation;
        }

        .trait-item:hover, .trait-item:active {
            background: rgba(255, 152, 0, 0.3);
            border-color: rgba(255, 152, 0, 0.5);
        }

        .trait-item.selected {
            background: rgba(255, 152, 0, 0.4);
            border-color: #FF9800;
        }

        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 10001;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .unsaved-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 152, 0, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-weight: bold;
            z-index: 9999;
            display: none;
        }

        .validation-error {
            border: 2px solid #f44336 !important;
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .success-flash {
            background: rgba(76, 175, 80, 0.3) !important;
            animation: flash 1s ease-in-out;
        }

        @keyframes flash {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        select, option {
            background-color: #18263c;
            color: #fff;
            border-radius: 8px;
            border: 2px solid #4caf50;
            padding: 8px;
        }

        select:focus {
            outline: 2px solid #2196F3;
        }

        .skill-usage-stats {
            background: rgba(76, 175, 80, 0.1);
            border: 2px solid rgba(76, 175, 80, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .skill-usage-stats h4 {
            color: #4CAF50;
            margin-bottom: 12px;
        }

        .skill-stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .skill-stat-bar {
            background: rgba(76, 175, 80, 0.3);
            height: 4px;
            border-radius: 2px;
            margin-top: 3px;
            transition: width 0.3s ease;
        }

        .outcome-analysis {
            background: rgba(33, 150, 243, 0.1);
            border: 2px solid rgba(33, 150, 243, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .outcome-analysis h4 {
            color: #2196F3;
            margin-bottom: 12px;
        }

        .outcome-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .outcome-probability {
            background: rgba(33, 150, 243, 0.3);
            color: #fff;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .header {
                padding: 0 10px;
                flex-wrap: wrap;
                height: auto;
                min-height: 70px;
            }

            .header h1 {
                font-size: 18px;
                margin-bottom: 5px;
                width: 100%;
                text-align: center;
            }

            .header-buttons {
                gap: 6px;
                flex-wrap: wrap;
                justify-content: center;
                width: 100%;
            }

            .btn {
                padding: 8px 12px;
                font-size: 12px;
            }

            .toggle-buttons {
                margin-right: 0;
                margin-bottom: 5px;
            }

            .container {
                grid-template-rows: auto 1fr;
            }

            .sidebar, .properties-panel {
                position: fixed;
                top: 0;
                height: 100vh;
                z-index: 2000;
                width: 320px;
                transform: translateX(-100%);
                opacity: 0;
                pointer-events: none;
            }

            .sidebar.visible {
                transform: translateX(0);
                opacity: 1;
                pointer-events: auto;
            }

            .properties-panel {
                right: 0;
                transform: translateX(100%);
                width: 350px;
            }

            .properties-panel.visible {
                transform: translateX(0);
                opacity: 1;
                pointer-events: auto;
            }

            .container.sidebar-collapsed,
            .container.properties-collapsed,
            .container.both-collapsed {
                grid-template-columns: 1fr;
            }

            .canvas-container {
                grid-column: 1;
            }

            .modal-content {
                margin: 5% auto;
                padding: 20px;
                width: 95%;
            }

            .connection-controls {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .skill-controls {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .input-group {
                flex-direction: column;
                gap: 8px;
            }

            .metadata-builder {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 16px;
            }

            .btn {
                padding: 6px 10px;
                font-size: 11px;
            }

            .sidebar, .properties-panel {
                width: 100%;
            }

            .form-group input,
            .form-group textarea,
            .form-group select {
                padding: 8px;
                font-size: 14px;
            }
        }

        /* Overlay for mobile when panels are open */
        .mobile-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1500;
        }

        @media (max-width: 768px) {
            .mobile-overlay.visible {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div class="mobile-overlay" id="mobileOverlay" onclick="closeMobilePanels()"></div>
    
    <div class="unsaved-indicator" id="unsavedIndicator">
        ⚠️ Unsaved Changes
    </div>

    <div class="container" id="mainContainer">
        <div class="header">
            <h1>🎯 Operation Homecoming</h1>
            <div class="toggle-buttons">
              <button class="toggle-btn" id="settingsBtn" onclick="openUserSettings()">⚙ Settings</button>
                <button class="toggle-btn active" id="sidebarToggle" onclick="toggleSidebar()">📋 Tools</button>
                <button class="toggle-btn active" id="propertiesToggle" onclick="toggleProperties()">⚙️ Props</button>
                <button class="toggle-btn" id="multiplayerToggle" onclick="toggleMultiplayer()">🌐 Multiplayer</button>
            </div>
            <div class="user-status" id="userStatus">
                <span style="color: #00ff00;">●</span> Anonymous (Single Player)
            </div>
            <div class="autosave-indicator" id="autosaveIndicator">
                <span id="autosaveText">💾 Autosaved</span>
            </div>
            <div class="header-buttons">
                <button class="btn btn-secondary" onclick="newStory()">📄 New</button>
                <button class="btn btn-primary" onclick="saveStory()">💾 Save</button>
                <button class="btn btn-secondary" onclick="loadStory()">📁 Load</button>
                <button class="btn btn-warning" onclick="openStoryBrowser()">📚 Browse</button>
                <button class="btn btn-danger" onclick="clearCanvas()">🗑️ Clear</button>
            </div>
        </div>

        <div class="sidebar" id="sidebar">
            <div class="sidebar-section">
                <h3>🎲 Node Types</h3>
                <div class="node-type" data-type="start">
                    <div class="node-color" style="background: #4CAF50;"></div>
                    <span>Start (Kidnapped)</span>
                </div>
                <div class="node-type" data-type="travel">
                    <div class="node-color" style="background: #FF9800;"></div>
                    <span>Travel Event</span>
                </div>
                <div class="node-type" data-type="camp">
                    <div class="node-color" style="background: #f44336;"></div>
                    <span>Camp Event</span>
                </div>
                <div class="node-type" data-type="rumor">
                    <div class="node-color" style="background: #9C27B0;"></div>
                    <span>Rumor/Failed Contact</span>
                </div>
                <div class="node-type" data-type="rescue">
                    <div class="node-color" style="background: #2196F3;"></div>
                    <span>Rescue Opportunity</span>
                </div>
                <div class="node-type" data-type="outcome">
                    <div class="node-color" style="background: #607D8B;"></div>
                    <span>Final Outcome</span>
                </div>
            </div>

            <div class="lobby-panel" id="lobbyPanel" style="display: none;">
                <h3>🌐 Multiplayer Lobby</h3>
                <div id="lobbyUsers">
                    <div style="color: #888; text-align: center; padding: 20px;">
                        No users connected
                    </div>
                </div>
            </div>

            <div class="story-browser">
                <h3>📚 Saved Stories</h3>
                <div class="story-list" id="storyList">
                    <div style="color: #888; text-align: center; padding: 20px;">
                        Loading stories...
                    </div>
                </div>
                <button class="btn btn-secondary btn-small" onclick="refreshStoryList()" style="width: 100%; margin-top: 8px;">
                    🔄 Refresh
                </button>
            </div>

            <div class="story-stats">
                <h3>📊 Story Statistics</h3>
                <div class="stat-row">
                    <span>Total Nodes:</span>
                    <span class="stat-value" id="nodeCount">0</span>
                </div>
                <div class="stat-row">
                    <span>Connections:</span>
                    <span class="stat-value" id="connectionCount">0</span>
                </div>
                <div class="stat-row">
                    <span>Outcomes:</span>
                    <span class="stat-value" id="outcomeCount">0</span>
                </div>
                <div class="stat-row">
                    <span>Skill-Enhanced:</span>
                    <span class="stat-value" id="skillEnhancedCount">0</span>
                </div>
                <div class="stat-row">
                    <span>Avg. Path Length:</span>
                    <span class="stat-value" id="avgPathLength">0 days</span>
                </div>
            </div>

            <div class="skill-usage-stats">
                <h4>🎯 Most Used Skills</h4>
                <div id="skillUsageList">
                    <div style="color: #888; text-align: center; padding: 10px;">No skills used yet</div>
                </div>
            </div>

            <div class="outcome-analysis">
                <h4>📈 Outcome Probabilities</h4>
                <div id="outcomeAnalysisList">
                    <div style="color: #888; text-align: center; padding: 10px;">No outcomes defined yet</div>
                </div>
            </div>
        </div>
        
        <div class="canvas-container" id="canvasContainer">
            <canvas id="storyCanvas"></canvas>
        </div>

        <div class="properties-panel" id="propertiesPanel">
            <h3>⚙️ Node Properties</h3>
            <div id="nodeProperties">
                <div style="text-align: center; margin-top: 50px; color: #888;">
                    <p>🎯 Select a node to edit its properties</p>
                    <p style="font-size: 12px; margin-top: 10px;">
                        Ctrl+Click to connect nodes<br>
                        Right-click to delete<br>
                        Drag to pan the map
                    </p>
                </div>
            </div>

            <div class="export-section">
                <h4>📤 Export Story Data</h4>
                <button class="btn btn-primary" onclick="exportStoryData()" style="width: 100%; margin-bottom: 10px;">
                    Generate JSON
                </button>
                <div class="json-output" id="jsonOutput">
                    Story data will appear here...
                </div>
            </div>
        </div>
    </div>

    <!-- Skill Requirements Modal -->
    <div id="skillModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎯 Skill Requirements Editor</h2>
                <span class="close" onclick="closeSkillModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="skillModalContent"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeSkillModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveSkillRequirements()">Save Changes</button>
            </div>
        </div>
    </div>

    <!-- Requirements Builder Modal -->
    <div id="requirementsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📋 Requirements Builder</h2>
                <span class="close" onclick="closeRequirementsModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="requirementsModalContent"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeRequirementsModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveRequirements()">Save Requirements</button>
            </div>
        </div>
    </div>

    <!-- Metadata Builder Modal -->
    <div id="metadataModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🏷️ Metadata Builder</h2>
                <span class="close" onclick="closeMetadataModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="metadataModalContent"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeMetadataModal()">Cancel</button>
                <button class="btn btn-primary" onclick="saveMetadata()">Save Metadata</button>
            </div>
        </div>
    </div>

    <!-- Story Browser Modal -->
    <div id="storyBrowserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📚 Story Browser</h2>
                <span class="close" onclick="closeStoryBrowser()">&times;</span>
            </div>
            <div class="modal-body">
                <div id="storyBrowserContent"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeStoryBrowser()">Close</button>
            </div>
        </div>
    </div>

    <!-- ─── USER SETTINGS MODAL (initially hidden) ────────────────────────────────── -->
<div class="modal" id="userSettingsModal">
  <div class="modal-content">
    <div class="modal-header">
      <h2>User Settings</h2>
    </div>
    <div class="modal-body">
      <div class="settings-item">
        <label for="userNameInput">Name:</label><br>
        <input type="text" id="userNameInput" placeholder="Enter your display name"
               style="width: 100%; padding: 8px; margin-top: 4px; border-radius: 4px; border: 1px solid #ccc;"/>
      </div>
      <div class="settings-item" style="margin-top: 15px;">
        <label for="userColorInput">Cursor Color:</label><br>
        <input type="color" id="userColorInput" value="#00ff00"
               style="width: 100%; height: 40px; border: none; padding: 0; margin-top: 4px;"/>
      </div>
    </div>
    <div class="modal-footer">
      <button id="saveUserSettingsBtn" onclick="saveUserSettings()">Save</button>
      <button onclick="closeUserSettings()">Cancel</button>
    </div>
  </div>
</div>
    <!-- ──────────────────────────────────────────────────────────────────────────── -->
    <!-- 1) Load Socket.IO client from CDN (no integrity attribute, to avoid mismatch)  -->
    <!--    Make sure this version roughly matches the Flask-SocketIO version on your server. -->
    <!-- ──────────────────────────────────────────────────────────────────────────── -->
    <script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>


    <script>
        // RimWorld Skills Data
        const RIMWORLD_SKILLS = {
            'Shooting': { category: 'Combat', description: 'Accuracy and aiming time with ranged weapons' },
            'Melee': { category: 'Combat', description: 'Damage, hit chance, and parry ability in close-quarters combat' },
            'Construction': { category: 'Work & Colony', description: 'Speed and quality of building, installing, and repairing structures' },
            'Mining': { category: 'Work & Colony', description: 'Speed of digging at resource veins or walls' },
            'Cooking': { category: 'Work & Colony', description: 'Meal preparation speed and chance to avoid food poisoning' },
            'Plants': { category: 'Work & Colony', description: 'Speed and success when sowing, harvesting, and cutting plants' },
            'Animals': { category: 'Work & Colony', description: 'Ability to tame, train, and handle animals' },
            'Crafting': { category: 'Work & Colony', description: 'Quality and speed when making clothing, weapons, and other crafted items' },
            'Artistic': { category: 'Work & Colony', description: 'Quality and beauty value of sculptures and art pieces' },
            'Medical': { category: 'Work & Colony', description: 'Speed and success of tending wounds, surgeries, and doctoring' },
            'Social': { category: 'Work & Colony', description: 'Persuasion and negotiation: recruiting prisoners, trading, forming caravans, and morale-boosting talks' },
            'Intellectual': { category: 'Work & Colony', description: 'Speed of researching new technologies' }
        };

        const SKILL_COMPETENCY = {
            0: 'Completely Incompetent', 1: 'Hopeless', 2: 'Awful', 3: 'Very Poor', 4: 'Poor', 5: 'Mediocre',
            6: 'Moderate', 7: 'Okay', 8: 'Decent', 9: 'Above Average', 10: 'Skilled', 11: 'Proficient',
            12: 'Very Skilled', 13: 'Expert', 14: 'Veteran', 15: 'Master', 16: 'Elite',
            17: 'Top Tier', 18: 'Legendary', 19: 'Mythic', 20: 'Godlike'
        };

        const CONNECTION_LABELS = ['Connection', 'Success', 'Failure', 'Partial Success', 'Critical Failure', 'Custom'];

        // Faction Data
        const FACTIONS = {
            'Tribal Raiders': {
                types: ['Gentle Tribe', 'Fierce Tribe', 'Savage Tribe', 'Cannibal Tribe'],
                category: 'Low-tech hunter-gatherers',
                dlc: 'Base Game'
            },
            'Outlanders': {
                types: ['Civil Outlanders', 'Minor Civil', 'Rough Outlanders', 'Refugee Outlanders'],
                category: 'Civilized wanderers',
                dlc: 'Base Game'
            },
            'Pirates': {
                types: ['Standard Pirates', 'Pirate Uprising'],
                category: 'Organized raiding crews',
                dlc: 'Base Game'
            },
            'Mercenaries': {
                types: ['Mercenary Bands'],
                category: 'Contract-for-hire fighters',
                dlc: 'Base Game'
            },
            'Spacer Scavengers': {
                types: ['Spacer Wreckage crews'],
                category: 'Stranded spacefarers',
                dlc: 'Base Game'
            },
            'Imperial Forces': {
                types: ['Imperial Yeomen', 'Imperial Guard', 'Stellarch\'s Troops'],
                category: 'Theocratic/royalist armies',
                dlc: 'Royalty DLC'
            },
            'Cultists': {
                types: ['Any cultist raiders (except Horax cult)'],
                category: 'Fanatical religious sects',
                dlc: 'Ideology DLC'
            }
        };

        // Traits Data
        const TRAITS = {
            'Lifestyle': {
                'Night Owl': 'Guards sleep by day, so this pawn is most alert when others are tired.',
                'Undergrounder': 'Not stressed by darkness or confinement.',
                'Nudist': 'Forced clothing causes more distress.'
            },
            'Living-Space': {
                'Ascetic': 'Not upset by spartan conditions.',
                'Homebody': 'Extra mood loss far from home.',
                'Jealous': 'Gets upset if others are treated better.',
                'Greedy': 'Tries to curry favor or hoard goods.'
            },
            'Social-View': {
                'Kind': 'Comforts others or bargains for treatment.',
                'Paranoid': 'Suspects sabotage or poison.',
                'Psychopath': 'May attack guards or scare others.',
                'Mean': 'Bullies other prisoners.'
            },
            'Combat-Mindset': {
                'Bloodlust': 'Tries to injure/kill captors.',
                'Brawler': 'Uses improvised weapons.',
                'Toolbrawler': 'Crafts weapons for escape.',
                'Pyromaniac': 'Tries to start fires as a distraction.'
            },
            'Skill-Related': {
                'Industrious': 'Digs, crafts tools for escape.',
                'Fast Learner': 'Quickly adapts to routines.',
                'Slow Learner': 'Slow to adapt.',
                'Intellectual': 'Plans and maps out escape routes.'
            },
            'Relationship-Driven': {
                'Codependent': 'Risks self for friends/loved ones.',
                'Kind': 'May refuse to leave others behind.',
                'Jealous': 'Undermines group if others favored.'
            }
        };

        // Mobile UI State
        let isMobile = window.innerWidth <= 768;
        let sidebarVisible = !isMobile;
        let propertiesVisible = !isMobile;

        // Update mobile state on resize
        window.addEventListener('resize', () => {
            const wasMobile = isMobile;
            isMobile = window.innerWidth <= 768;
            
            if (wasMobile !== isMobile) {
                // Reset visibility states when switching between mobile/desktop
                if (isMobile) {
                    sidebarVisible = false;
                    propertiesVisible = false;
                    updatePanelVisibility();
                } else {
                    sidebarVisible = true;
                    propertiesVisible = true;
                    updatePanelVisibility();
                }
            }
        });

        function toggleSidebar() {
            sidebarVisible = !sidebarVisible;
            updatePanelVisibility();
        }

        function toggleProperties() {
            propertiesVisible = !propertiesVisible;
            updatePanelVisibility();
        }

        function updatePanelVisibility() {
            const container = document.getElementById('mainContainer');
            const sidebar = document.getElementById('sidebar');
            const propertiesPanel = document.getElementById('propertiesPanel');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const propertiesToggle = document.getElementById('propertiesToggle');
            const mobileOverlay = document.getElementById('mobileOverlay');

            if (isMobile) {
                // Mobile layout
                sidebar.classList.toggle('visible', sidebarVisible);
                propertiesPanel.classList.toggle('visible', propertiesVisible);
                mobileOverlay.classList.toggle('visible', sidebarVisible || propertiesVisible);
                
                container.className = 'container both-collapsed';
            } else {
                // Desktop layout
                sidebar.classList.remove('visible');
                propertiesPanel.classList.remove('visible');
                mobileOverlay.classList.remove('visible');
                
                if (!sidebarVisible && !propertiesVisible) {
                    container.className = 'container both-collapsed';
                } else if (!sidebarVisible) {
                    container.className = 'container sidebar-collapsed';
                } else if (!propertiesVisible) {
                    container.className = 'container properties-collapsed';
                } else {
                    container.className = 'container';
                }
            }

            // Update toggle button states
            sidebarToggle.classList.toggle('active', sidebarVisible);
            propertiesToggle.classList.toggle('active', propertiesVisible);

            // Resize canvas if storyBuilder exists
            if (typeof storyBuilder !== 'undefined' && storyBuilder) {
                setTimeout(() => {
                    storyBuilder.setupCanvas();
                    storyBuilder.render();
                }, 300);
            }
        }

        function closeMobilePanels() {
            if (isMobile) {
                sidebarVisible = false;
                propertiesVisible = false;
                updatePanelVisibility();
            }
        }

        // Database for persistent storage
        class StoryDatabase {
            constructor() {
                this.dbName = 'OperationHomecomingStories';
                this.version = 1;
                this.db = null;
                this.init();
            }

            async init() {
                return new Promise((resolve, reject) => {
                    const request = indexedDB.open(this.dbName, this.version);
                    
                    request.onerror = () => reject(request.error);
                    request.onsuccess = () => {
                        this.db = request.result;
                        resolve();
                    };
                    
                    request.onupgradeneeded = (event) => {
                        const db = event.target.result;
                        if (!db.objectStoreNames.contains('stories')) {
                            const store = db.createObjectStore('stories', { keyPath: 'name' });
                            store.createIndex('created', 'created', { unique: false });
                        }
                    };
                });
            }

            async saveStory(name, data) {
                if (!this.db) await this.init();
                
                return new Promise((resolve, reject) => {
                    const transaction = this.db.transaction(['stories'], 'readwrite');
                    const store = transaction.objectStore('stories');
                    
                    const storyData = {
                        name: name,
                        data: data,
                        created: Date.now(),
                        modified: Date.now()
                    };
                    
                    const request = store.put(storyData);
                    request.onsuccess = () => resolve();
                    request.onerror = () => reject(request.error);
                });
            }

            async loadStory(name) {
                if (!this.db) await this.init();
                
                return new Promise((resolve, reject) => {
                    const transaction = this.db.transaction(['stories'], 'readonly');
                    const store = transaction.objectStore('stories');
                    
                    const request = store.get(name);
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
            }

            async getAllStories() {
                if (!this.db) await this.init();
                
                return new Promise((resolve, reject) => {
                    const transaction = this.db.transaction(['stories'], 'readonly');
                    const store = transaction.objectStore('stories');
                    
                    const request = store.getAll();
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
            }

            async deleteStory(name) {
                if (!this.db) await this.init();
                
                return new Promise((resolve, reject) => {
                    const transaction = this.db.transaction(['stories'], 'readwrite');
                    const store = transaction.objectStore('stories');
                    
                    const request = store.delete(name);
                    request.onsuccess = () => resolve();
                    request.onerror = () => reject(request.error);
                });
            }
        }

        class StoryBuilder {
          constructor() {
         
    // ─────────────────────────────────────────────────────────────────
    //  CORE CANVAS & DATA FIELDS
    // ─────────────────────────────────────────────────────────────────
    this.canvas = document.getElementById('storyCanvas');
    console.log('>> constructor: this.canvas =', this.canvas);
    this.ctx = this.canvas.getContext('2d');

    this.nodes = [];
    this.connections = [];

    this.selectedNodes = [];
    this.draggedNode = null;
    this.draggedNodes = null;
    this.draggedNodesInitial = [];
    this.groupDragStartMouseWorldX = 0;
    this.groupDragStartMouseWorldY = 0;
    this.isDraggingGroup = false;
    this.draggedGroup = null;
    this.isResizingGroup = false;
    this.resizingGroupHandle = null;

    // Per-type node counters for proper naming
    this.nodeTypeCounters = {
      'start': 0,
      'travel': 0,
      'camp': 0,
      'rumor': 0,
      'rescue': 0,
      'outcome': 0
    };

    this.nextNodeId = 1;
    // ─── NEW for BOX-SELECTION ────────────────────────────────────────
    // Are we currently dragging out a rectangular selection?
    this.isBoxSelecting = false;

    // Starting mouse position (screen coords, relative to the canvas) when shift+drag begins
    this.boxStartX = 0;
    this.boxStartY = 0;

    // Current mouse position (screen coords) while dragging the selection box
    this.boxCurrentX = 0;
    this.boxCurrentY = 0;

    // ─── NEW: store this user’s display name & cursor color ───────────
    // Default to “Anonymous” and a green cursor.  If you want to persist
    // across page‐reload, you could also read from localStorage here.
    this.userName       = 'Anonymous';
    this.myCursorColor  = '#00ff00';

    // ─────────────────────────────────────────────────────────────────
    //  UNDO (Ctrl+Z) HISTORY
    // ─────────────────────────────────────────────────────────────────
    this.history = [];
    this.historyIndex = -1;
    this.recordHistory();  

    // ─────────────────────────────────────────────────────────────────
    //  COPY / PASTE (Ctrl+C / Ctrl+V)
    // ─────────────────────────────────────────────────────────────────
    this.copiedNodes = [];
    this.copyReferenceWorldX = 0;     // World‐X of the first node when copying
    this.copyReferenceWorldY = 0;     // World‐Y of the first node when copying
    this.lastMouseWorldX = 0;         // Continuously updated on mousemove
    this.lastMouseWorldY = 0;

    // ─────────────────────────────────────────────────────────────────
    //  CANVAS TRANSFORM & PANNING KEYS
    // ─────────────────────────────────────────────────────────────────
    this.scale = 1;
    this.offsetX = 200;
    this.offsetY = 200;
    this.isPanning = false;
    this.lastPanX = 0;
    this.lastPanY = 0;

    // Modifier keys
    this.isCtrlPressed = false;

    // Unsaved changes & autosave
    this.hasUnsavedChanges = false;
    this.autosaveInterval = null;
    this.autosaveTimeout = null;

    // Current editing context
    this.currentEditingNode = null;

    // Persistent storage
    this.database = new StoryDatabase();
    this.currentStoryName = null;

    // RIMWORLD_SKILLS, SKILL_COMPETENCY, CONNECTION_LABELS, FACTIONS, TRAITS, nodeTypes
    this.nodeTypes = {
      start:   { color: '#4CAF50', label: 'Start' },
      travel:  { color: '#FF9800', label: 'Travel' },
      camp:    { color: '#f44336', label: 'Camp' },
      rumor:   { color: '#9C27B0', label: 'Rumor' },
      rescue:  { color: '#2196F3', label: 'Rescue' },
      outcome: { color: '#607D8B', label: 'Outcome' }
    };
    this.CONNECTION_LABELS = [
      'Connection', 'Success', 'Failure',
      'Partial Success', 'Critical Failure', 'Custom'
    ];

    // ─────────────────────────────────────────────────────────────────
    //  GLOBAL KEYDOWN SHORTCUTS (Undo, Copy, Paste, Ctrl detection)
    // ─────────────────────────────────────────────────────────────────
    document.addEventListener('keydown', (e) => {
  // ───────────────────────────────────────────────────────────────────────────
  // 1) UNDO: Ctrl+Z (or Cmd+Z on Mac)
  // ───────────────────────────────────────────────────────────────────────────
  if ((e.ctrlKey || e.metaKey) && e.key === 'z') {
    e.preventDefault();
    this.undo();
    return;
  }

  // ───────────────────────────────────────────────────────────────────────────
  // 2) COPY: Ctrl+C (or Cmd+C on Mac)
  // ───────────────────────────────────────────────────────────────────────────
  if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
    e.preventDefault();
    this.copySelectedNodes();
    return;
  }

  // ───────────────────────────────────────────────────────────────────────────
  // 3) PASTE: Ctrl+V (or Cmd+V on Mac)
  // ───────────────────────────────────────────────────────────────────────────
  if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
    e.preventDefault();
    this.pasteNodes();
    return;
  }

  // ───────────────────────────────────────────────────────────────────────────
  // 4) DELETE: Delete key removes all currently selected nodes
  // ───────────────────────────────────────────────────────────────────────────
  if (e.key === 'Delete') {
    if (this.selectedNodes.length > 0) {
      this.deleteSelectedNodes();
    }
    return;
  }

  // ───────────────────────────────────────────────────────────────────────────
  // 5) CONNECTION LABEL HOTKEYS: Ctrl+1 through Ctrl+9
  // ───────────────────────────────────────────────────────────────────────────
  if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '9') {
    e.preventDefault();
    this.handleConnectionLabelHotkey(parseInt(e.key) - 1);
    return;
  }

  // ───────────────────────────────────────────────────────────────────────────
  // 6) Track whether the Control key is held (for Ctrl+Click connections, etc.)
  // ───────────────────────────────────────────────────────────────────────────
  if (e.key === 'Control') {
    this.isCtrlPressed = true;
  }
});

document.addEventListener('keyup', (e) => {
  if (e.key === 'Control') {
    this.isCtrlPressed = false;
  }
});

// Prevent data loss on accidental tab/browser close
window.addEventListener('beforeunload', (e) => {
  if (this.hasUnsavedChanges) {
    e.preventDefault();
    e.returnValue = '';
  }
});


      // ─────────────────────────────────────────────────────────────────
  //  INITIALIZE CANVAS, EVENTS, AUTOSAVE, ETC.
  // ─────────────────────────────────────────────────────────────────
  this.setupCanvas();
  console.log('>> constructor: this.canvas =', this.canvas);
console.log('>> constructor: about to bind event listeners');
  this.setupEventListeners();
  this.canvas.addEventListener('mousedown', e => {
  console.log('*** minimal test: canvas got a mousedown at', e.clientX, e.clientY);
});
  this.setupAutosave();
  this.refreshStoryList();
  this.render();

  // ─────────────────────────────────────────────────────────────────
  //  SET UP WebSocket CONNECTION TO SERVER
  // ─────────────────────────────────────────────────────────────────
  this.socket = io(); // Connect to ws://localhost:5000 by default

  // Initialize multiplayer state - default to multiplayer
  this.isMultiplayerMode = true;
  this.currentLobbyId = null;
  this.lobbyUsers = {};
  this.remoteSelections = {}; // { userId: { nodeId, userColor } }
  this.sessionToken = localStorage.getItem('multiplayerSessionToken');
  this.pendingSubGraphs = []; // Array of pending story overlays

  // ─── NEW: Cursor throttling and drag state management ─────────────
  this.lastCursorEmitTime = 0;
  this.cursorThrottleInterval = 16; // ~60fps
  this.isDragging = false;
  this.queuedRemoteEvents = []; // Queue events during local drag

  // ─── NEW: Box selection broadcasting ─────────────
  this.remoteBoxSelections = {}; // { userId: { x1, y1, x2, y2, userColor } }

  // ─── NEW: Story groups ─────────────
  this.storyGroups = []; // Array of group objects
  this.nextGroupId = 1;
  this.selectedGroup = null;

  // ─── NEW: Node creation drag and drop ─────────────
  this.isDraggingNewNode = false;
  this.newNodeType = null;
  this.ghostNode = null;

  // ─── NEW: Live cursor coordinates ─────────────
  this.showCursorCoordinates = true;
  this.currentMouseWorldX = 0;
  this.currentMouseWorldY = 0;

  // Initialize periodic render for multiplayer updates
  this.initializePeriodicRender();

  // 1) When first connecting, server sends us the full master state
  this.socket.on('full-state', (data) => {
    if (this.isDragging) {
      this.queuedRemoteEvents.push({ type: 'full-state', data });
      return;
    }
    this.nodes = JSON.parse(JSON.stringify(data.nodes));
    this.connections = JSON.parse(JSON.stringify(data.connections));
    this.storyGroups = JSON.parse(JSON.stringify(data.groups || []));

    // Auto-attach nodes to groups based on position
    this.autoAttachNodesToGroups();

    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  });

  // 2) When another client pushes a story‐update, apply it
  this.socket.on('story-update', (data) => {
    if (this.isDragging) {
      this.queuedRemoteEvents.push({ type: 'story-update', data });
      return;
    }
    this.nodes = JSON.parse(JSON.stringify(data.nodes));
    this.connections = JSON.parse(JSON.stringify(data.connections));
    this.storyGroups = JSON.parse(JSON.stringify(data.groups || []));

    // Auto-attach nodes to groups based on position
    this.autoAttachNodesToGroups();

    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  });

   // ─── REMOTE CURSOR MOVES now include “name” and “color” ─────────────
   this.socket.on('cursor-move', (data) => {
      // data: { sid, x, y, color, name }
      if (!this.remoteCursors) this.remoteCursors = {};
      this.remoteCursors[data.sid] = {
        x: data.x,
        y: data.y,
        color: data.color,
        name: data.name
      };
    });

  // 4) When a client disconnects, remove its cursor
  this.socket.on('cursor-remove', (data) => {
    // data = { sid: <socketId> }
    if (this.remoteCursors) delete this.remoteCursors[data.sid];
  });

  // ─── ENHANCED NODE SELECTION EVENTS ─────────────
  this.socket.on('node-selected', (data) => {
    // data: { nodeId, userId, userColor }
    this.remoteSelections[data.userId] = {
      nodeId: data.nodeId,
      userColor: data.userColor
    };
    this.render(); // Re-render to show selection highlights
  });

  this.socket.on('node-deselected', (data) => {
    // data: { nodeId, userId }
    if (this.remoteSelections[data.userId]) {
      delete this.remoteSelections[data.userId];
      this.render(); // Re-render to remove highlights
    }
  });

  // ─── LOBBY MANAGEMENT EVENTS ─────────────
  this.socket.on('lobby-state', (data) => {
    // data: { lobbyId, users, userSelections }
    this.currentLobbyId = data.lobbyId;
    this.lobbyUsers = data.users;
    this.updateLobbyUI();
  });

  this.socket.on('user-joined', (data) => {
    // data: { userId, username, userColor }
    this.lobbyUsers[data.userId] = {
      username: data.username,
      userColor: data.userColor
    };
    this.updateLobbyUI();
    this.showToast(`${data.username} joined the lobby`, 'info');
  });

  this.socket.on('user-left', (data) => {
    // data: { sid }
    if (this.lobbyUsers[data.sid]) {
      const username = this.lobbyUsers[data.sid].username;
      delete this.lobbyUsers[data.sid];
      delete this.remoteSelections[data.sid];
      this.updateLobbyUI();
      this.showToast(`${username} left the lobby`, 'info');
      this.render(); // Remove their selections
    }
  });

  // ─── USER INFO MANAGEMENT ─────────────
  this.socket.on('user-info-confirmed', (data) => {
    // data: { username, userColor, sessionToken }
    this.userName = data.username;
    this.myCursorColor = data.userColor;
    this.sessionToken = data.sessionToken;
    localStorage.setItem('multiplayerSessionToken', data.sessionToken);
    this.updateUserStatusDisplay();
  });

  this.socket.on('user-info-restored', (data) => {
    // data: { username, userColor, sessionToken }
    this.userName = data.username;
    this.myCursorColor = data.userColor;
    this.sessionToken = data.sessionToken;
    this.updateUserStatusDisplay();
    this.showToast(`Welcome back, ${data.username}!`, 'success');
  });

  // ─── STORY LOADING EVENTS ─────────────
  this.socket.on('story-loaded', (data) => {
    // data: { storyJSON, initiatorId, storyName, storyId }
    this.handleIncomingStoryLoad(data);
  });

  // ─── ERROR HANDLING ─────────────
  this.socket.on('error', (data) => {
    // data: { code, message }
    this.showToast(`Error: ${data.message}`, 'error');
  });

  // Try to restore session on connect
  if (this.sessionToken) {
    this.socket.emit('reconnect-request', { sessionToken: this.sessionToken });
  }

  // ─── NEW: Enhanced multiplayer events ─────────────

  // Node creation with server-assigned IDs
  this.socket.on('node-created', (data) => {
    if (this.isDragging) {
      this.queuedRemoteEvents.push({ type: 'node-created', data });
      return;
    }
    // data: { nodeID, type, x, y, createdBy, title, ... }
    const newNode = {
      id: data.nodeID,
      type: data.type,
      x: data.x,
      y: data.y,
      title: data.title,
      description: '',
      probability: data.type === 'start' ? 100 : 50,
      requirements: [],
      outcomes: [],
      metadata: {},
      skillEffects: [],
      nodeSkillRequirements: [],
      connectionSkillRequirements: {},
      faction: data.type === 'start' ? 'Pirates' : null,
      traits: [],
      factionRelationshipModifiers: {}
    };
    this.nodes.push(newNode);
    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  });

  // Connection removal
  this.socket.on('connection-removed', (data) => {
    if (this.isDragging) {
      this.queuedRemoteEvents.push({ type: 'connection-removed', data });
      return;
    }
    // data: { from, to }
    this.connections = this.connections.filter(conn =>
      !(conn.from === data.from && conn.to === data.to)
    );
    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  });

  // Box selection broadcasting
  this.socket.on('box-selecting', (data) => {
    // data: { x1, y1, x2, y2, userID, userColor }
    this.remoteBoxSelections[data.userID] = {
      x1: data.x1,
      y1: data.y1,
      x2: data.x2,
      y2: data.y2,
      userColor: data.userColor
    };
    this.render();
  });

  // Box selection completion
  this.socket.on('box-select-complete', (data) => {
    // data: { selectedNodeIDs, userID }
    // Clear the box selection for this user
    if (this.remoteBoxSelections[data.userID]) {
      delete this.remoteBoxSelections[data.userID];
      this.render();
    }
  });



  // Connection label changes
  this.socket.on('connection-label-changed', (data) => {
    if (this.isDragging) {
      this.queuedRemoteEvents.push({ type: 'connection-label-changed', data });
      return;
    }
    // data: { connectionID, newLabel, changedBy }
    const conn = this.connections.find(c =>
      c.from === data.connectionID.from && c.to === data.connectionID.to
    );
    if (conn) {
      conn.label = data.newLabel;
      this.updatePropertiesPanel();
      this.render();
    }
  });

  // Group creation
  this.socket.on('group-created', (data) => {
    // data: { groupID, name, color, childNodeIDs, createdBy }
    const group = {
      id: data.groupID,
      name: data.name,
      color: data.color,
      childNodeIDs: data.childNodeIDs,
      createdBy: data.createdBy,
      isEditing: false,
      // Calculate bounding box from child nodes
      x: 0, y: 0, width: 100, height: 100
    };

    // Calculate actual bounding box
    if (data.childNodeIDs.length > 0) {
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
      data.childNodeIDs.forEach(nodeId => {
        const node = this.nodes.find(n => n.id === nodeId);
        if (node) {
          minX = Math.min(minX, node.x - 40);
          minY = Math.min(minY, node.y - 40);
          maxX = Math.max(maxX, node.x + 40);
          maxY = Math.max(maxY, node.y + 40);
        }
      });

      group.x = minX;
      group.y = minY - 30;
      group.width = maxX - minX;
      group.height = maxY - minY + 30;
      group.dragHandles = this.createGroupDragHandles(group.x, group.y, group.width, group.height);
    }

    this.storyGroups.push(group);
    this.render();
  });

  // Group movement
  this.socket.on('group-moved', (data) => {
    // data: { groupID, deltaX, deltaY, newX, newY, movedBy }
    const group = this.storyGroups.find(g => g.id === data.groupID);
    if (group && data.movedBy !== this.socket.id) {
      // Move the group
      group.x = data.newX;
      group.y = data.newY;

      // Move child nodes
      group.childNodeIDs.forEach(nodeId => {
        const node = this.nodes.find(n => n.id === nodeId);
        if (node) {
          node.x += data.deltaX;
          node.y += data.deltaY;
        }
      });

      // Update drag handles
      group.dragHandles = this.createGroupDragHandles(group.x, group.y, group.width, group.height);

      // Force render to show changes
      this.render();

      // Broadcast the updated state to keep everyone in sync
      if (this.socket) {
        this.socket.emit('story-update', {
          nodes: this.nodes,
          connections: this.connections,
          groups: this.storyGroups || []
        });
      }
    }
  });

  // Group updates
  this.socket.on('group-updated', (data) => {
    // data: { groupID, property, value, updatedBy }
    const group = this.storyGroups.find(g => g.id === data.groupID);
    if (group && data.updatedBy !== this.socket.id) {
      group[data.property] = data.value;

      // Update drag handles if position/size changed
      if (['x', 'y', 'width', 'height'].includes(data.property)) {
        group.dragHandles = this.createGroupDragHandles(group.x, group.y, group.width, group.height);
      }

      this.render();

      // Update properties panel if this group is selected
      if (this.selectedGroup && this.selectedGroup.id === data.groupID) {
        this.updatePropertiesPanel();
      }
    }
  });

  // Group deletion
  this.socket.on('group-deleted', (data) => {
    // data: { groupID, deletedBy }
    if (data.deletedBy !== this.socket.id) {
      this.storyGroups = this.storyGroups.filter(g => g.id !== data.groupID);

      // Clear selection if deleted group was selected
      if (this.selectedGroup && this.selectedGroup.id === data.groupID) {
        this.selectedGroup = null;
        this.updatePropertiesPanel();
      }

      this.render();
    }
  });
}

  // ─────────────────────────────────────────────────────────────────
  // HISTORY: recordHistory() and undo()
  // ─────────────────────────────────────────────────────────────────

  recordHistory() {
  // If we undid some steps and then do a new action, truncate “future” states
  if (this.historyIndex < this.history.length - 1) {
    this.history.splice(this.historyIndex + 1);
  }

  // Deep‐clone nodes and connections
  const snapshot = {
    nodes: JSON.parse(JSON.stringify(this.nodes)),
    connections: JSON.parse(JSON.stringify(this.connections))
  };

  this.history.push(snapshot);
  this.historyIndex = this.history.length - 1;

  // Cap history length at 50 to avoid excessive memory
  if (this.history.length > 50) {
    this.history.shift();
    this.historyIndex--;
  }

  // ─────────────────────────────────────────────────────────────────
  //  BROADCAST the new full story state to all other clients
  // ─────────────────────────────────────────────────────────────────
  if (this.socket) {
    this.socket.emit('story-update', {
      nodes: this.nodes,
      connections: this.connections,
      groups: this.storyGroups || []
    });
  }
}


/**
 * Undo to previous snapshot, if available.
 * Decrements historyIndex and restores nodes+connections.
 */
undo() {
  if (this.historyIndex <= 0) {
    // No earlier snapshot to revert to
    return;
  }

  this.historyIndex--;
  const snapshot = this.history[this.historyIndex];
  if (!snapshot) return;

  // Restore deep-cloned data
  this.nodes = JSON.parse(JSON.stringify(snapshot.nodes));
  this.connections = JSON.parse(JSON.stringify(snapshot.connections));

  // Clear any drag/selection state
  this.selectedNodes = [];
  this.draggedNode = null;
  this.draggedNodes = null;
  this.draggedNodesInitial = [];

  // Update UI
  this.updatePropertiesPanel();
  this.updateStats();
  this.render();
}

// ─────────────────────────────────────────────────────────────────
//  COPY / PASTE: copySelectedNodes() & pasteNodes()
// ─────────────────────────────────────────────────────────────────

/**
 * Deep-clone each node in this.selectedNodes into this.copiedNodes.
 * Also copies connections between selected nodes.
 */
 copySelectedNodes() {
  if (this.selectedNodes.length === 0) return;

  // Get IDs of selected nodes
  const selectedNodeIds = this.selectedNodes.map(node => node.id);

  // Deep‐clone each selected node
  this.copiedNodes = this.selectedNodes.map(node =>
    JSON.parse(JSON.stringify(node))
  );

  // Copy connections between selected nodes
  this.copiedConnections = this.connections.filter(conn =>
    selectedNodeIds.includes(conn.from) && selectedNodeIds.includes(conn.to)
  ).map(conn => JSON.parse(JSON.stringify(conn)));

  // Use the first selected node as the reference for offset calculations
  const refNode = this.selectedNodes[0];
  this.copyReferenceWorldX = refNode.x;
  this.copyReferenceWorldY = refNode.y;
}



/**
 * For each object in this.copiedNodes, assign a new ID, offset its
 * x/y by +20px in world coords, and push into this.nodes. Then re-select
 * newly pasted nodes. Also paste connections between the nodes.
 */
 pasteNodes() {
  if (!this.copiedNodes || this.copiedNodes.length === 0) return;

  const pasted = [];
  const idMapping = {}; // Map old IDs to new IDs

  // Compute offset so that (copyReferenceWorldX, copyReferenceWorldY)
  // maps to (lastMouseWorldX, lastMouseWorldY)
  const offsetX = this.lastMouseWorldX - this.copyReferenceWorldX;
  const offsetY = this.lastMouseWorldY - this.copyReferenceWorldY;

  // First pass: create nodes with new IDs, ensuring no collisions
  const existingNodeIds = new Set(this.nodes.map(node => node.id));

  this.copiedNodes.forEach(clone => {
    const oldId = clone.id;
    let newId;

    // Generate unique ID - check for collisions
    do {
      newId = this.nextNodeId++;
    } while (existingNodeIds.has(newId));

    idMapping[oldId] = newId;
    existingNodeIds.add(newId); // Track this new ID

    const newNode = JSON.parse(JSON.stringify(clone));
    newNode.id = newId;

    // Offset the clone’s position so the reference node ends up under the cursor
    newNode.x = (newNode.x || 0) + offsetX;
    newNode.y = (newNode.y || 0) + offsetY;

    this.nodes.push(newNode);
    pasted.push(newNode);
  });

  // Second pass: create connections with updated IDs
  if (this.copiedConnections) {
    this.copiedConnections.forEach(connClone => {
      const newConnection = JSON.parse(JSON.stringify(connClone));
      newConnection.from = idMapping[connClone.from];
      newConnection.to = idMapping[connClone.to];

      // Only add if both nodes were pasted
      if (newConnection.from && newConnection.to) {
        this.connections.push(newConnection);
      }
    });
  }

  // After pasting, select the newly pasted nodes
  this.selectedNodes = pasted.slice();

  // Record this paste in history & broadcast
  this.markUnsaved();
  this.recordHistory();

  // Update UI
  this.updatePropertiesPanel();
  this.updateStats();
  this.render();
}



  // ─────────────────────────────────────────────────────────────────
  // CANVAS & EVENT LISTENERS
  // ─────────────────────────────────────────────────────────────────

  setupCanvas() {
    const container = document.getElementById('canvasContainer');
    this.canvas.width = container.offsetWidth;
    this.canvas.height = container.offsetHeight;
  }

  // ─────────────────────────────────────────────────────────────────
// CANVAS & EVENT LISTENERS (fixed)
// ─────────────────────────────────────────────────────────────────
setupEventListeners() {
  console.log('>> setupEventListeners() called; this.canvas =', this.canvas);
  
  // Only attach these once the user has joined (i.e. after set-user-info)
  // We guard with a flag that we flip in handleJoin() when “Join” is clicked.
  const attachCanvasListeners = () => {
    this.canvas.addEventListener('mousedown',   (e) => this.handleMouseDown(e));
    this.canvas.addEventListener('mousemove',   (e) => this.handleMouseMove(e));
    this.canvas.addEventListener('mouseup',     (e) => this.handleMouseUp(e));
    this.canvas.addEventListener('wheel',       (e) => this.handleWheel(e));
    this.canvas.addEventListener('contextmenu', (e) => this.handleContextMenu(e));

    this.canvas.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: false });
    this.canvas.addEventListener('touchmove',  (e) => this.handleTouchMove(e),  { passive: false });
    this.canvas.addEventListener('touchend',   (e) => this.handleTouchEnd(e),   { passive: false });

    document.addEventListener('mousemove', (e) => this.handleGlobalMouseMove(e));
    document.addEventListener('mouseup',   (e) => this.handleGlobalMouseUp(e));
  };

  // If already joined (sessionToken exists), attach immediately;
  // otherwise, wait for the “join” button to fire.
  // Always attach listeners immediately
  attachCanvasListeners();

  // Listen for when the user clicks “Join” in the modal:
  // (We’ll set up a one-time listener on the join button here)
  document.addEventListener('DOMContentLoaded', () => {
    const joinBtn = document.getElementById('joinBtn');
    if (joinBtn) {
      joinBtn.addEventListener('click', () => {
        // Delay just a tick to let the modal be removed, then attach listeners
        setTimeout(attachCanvasListeners, 100);
      });
    }
  });
}


  

  // Touch handling (for completeness; panning similar to mouse)
  handleTouchStart(e) {
    e.preventDefault();
    const touch = e.touches[0];
    const rect = this.canvas.getBoundingClientRect();
    const x = (touch.clientX - rect.left - this.offsetX) / this.scale;
    const y = (touch.clientY - rect.top - this.offsetY) / this.scale;

    const clickedNode = this.getNodeAt(x, y);

    if (clickedNode) {
      this.selectedNodes = [clickedNode];
      this.draggedNode = clickedNode;
      this.updatePropertiesPanel();
    } else {
      this.selectedNodes = [];
      this.draggedNode = null;
      this.updatePropertiesPanel();
      this.isPanning = true;
      this.lastPanX = touch.clientX;
      this.lastPanY = touch.clientY;
      document.getElementById('canvasContainer').classList.add('panning');
    }
    this.render();
  }

  handleTouchMove(e) {
    e.preventDefault();
    const touch = e.touches[0];

    if (this.draggedNode && !this.isPanning) {
      const rect = this.canvas.getBoundingClientRect();
      const x = (touch.clientX - rect.left - this.offsetX) / this.scale;
      const y = (touch.clientY - rect.top - this.offsetY) / this.scale;
      this.draggedNode.x = x;
      this.draggedNode.y = y;
      this.markUnsaved();
      this.render();
    } else if (this.isPanning && !this.draggedNode && !this.draggedNodes) {
      const deltaX = touch.clientX - this.lastPanX;
      const deltaY = touch.clientY - this.lastPanY;
      this.offsetX += deltaX;
      this.offsetY += deltaY;
      this.lastPanX = touch.clientX;
      this.lastPanY = touch.clientY;
      this.render();
    }
  }

  handleTouchEnd(e) {
    e.preventDefault();
    this.draggedNode = null;
    this.isPanning = false;
    if (e.nativeEvent.changedTouches.length === 1 && this.selectedNodes.length > 1) {
      // For multi-select, you could implement multi-connection on double-tap, etc.
    }
    document.getElementById('canvasContainer').classList.remove('panning');

    // Record history if a group-drag was in progress
    if (this.draggedNodes) {
      this.draggedNodes = null;
      this.draggedNodesInitial = [];
      this.recordHistory();
    }
  }

 /**
 * handleMouseDown: supports:
 *   • Shift+drag for rubber-band selection (NEW)
 *   • Shift+click to toggle single node in/out of selection (existing)
 *   • Ctrl+click to connect nodes (existing)
 *   • Single click → select/drag one node (existing)
 *   • Empty-space click → clear selection + start panning (modified)
 *   • Middle-click → always pan (existing)
 */
handleMouseDown(e) {
  console.log(
    '>> handleMouseDown (raw) fired; isMultiplayerMode=',
    this.isMultiplayerMode,
    ' sessionToken=',
    this.sessionToken,
    ' userName=',
    this.userName
  );
  // If in multiplayer mode but the user hasn’t finished joining:
  // COMMENTED OUT: Allow interaction in both single-player and multiplayer modes
  // if (this.isMultiplayerMode && (!this.sessionToken || this.userName === 'Anonymous')) {
  //   console.log('>> handleMouseDown: bailing because user has not joined multiplayer yet');
  //   // Early‐out: block any clicks until join is complete.
  //   e.preventDefault();
  //   return;
  // }

  // Compute (x, y) in WORLD COORDINATES from the mouse event.
  const rect   = this.canvas.getBoundingClientRect();
  const xWorld = (e.clientX - rect.left - this.offsetX) / this.scale;
  const yWorld = (e.clientY - rect.top  - this.offsetY) / this.scale;

  // Store current mouse position for coordinate display and hotkeys
  this.lastMouseWorldX = xWorld;
  this.lastMouseWorldY = yWorld;

  // ─────────────────────────────────────────────────────────
  //  0) Check if we clicked on a connection label
  // ─────────────────────────────────────────────────────────
  if (this.connectionLabelAreas) {
    const clickedLabel = this.connectionLabelAreas.find(area => {
      const rect = this.canvas.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      return mouseX >= area.x && mouseX <= area.x + area.width &&
             mouseY >= area.y && mouseY <= area.y + area.height;
    });

    if (clickedLabel) {
      // Open connection editing window
      this.openConnectionEditWindow(clickedLabel.connection);
      return;
    }
  }

  const clickedNode = this.getNodeAt(xWorld, yWorld);
  const clickedGroup = this.getGroupAt(xWorld, yWorld);
  const clickedGroupTitle = this.getGroupTitleAt(xWorld, yWorld);
  const clickedGroupHandle = this.getGroupHandleAt(xWorld, yWorld);
  console.log('   clickedNode =', clickedNode, ' clickedGroup =', clickedGroup, ' clickedGroupTitle =', clickedGroupTitle, ' shiftKey=', e.shiftKey, ' ctrlKey=', e.ctrlKey);


  // ——————————————————————————————————————————————
  //  A) MIDDLE-CLICK (button === 1): always pan
  // ——————————————————————————————————————————————
  if (e.button === 1) {
    e.preventDefault();
    this.selectedNodes = [];
    this.draggedNode   = null;
    this.updatePropertiesPanel();

    this.isPanning = true;
    this.lastPanX = e.clientX;
    this.lastPanY = e.clientY;
    document.getElementById('canvasContainer').classList.add('panning');
    return;
  }

  // ——————————————————————————————————————————————
  //  B) LEFT-CLICK (button === 0)
  // ——————————————————————————————————————————————
  if (e.button === 0) {
    // 1) SHIFT + CLICK ON AN EXISTING NODE → toggle it in selectedNodes
    if (clickedNode && e.shiftKey) {
      const idx = this.selectedNodes.findIndex(n => n.id === clickedNode.id);
      if (idx >= 0) {
        this.selectedNodes.splice(idx, 1);
      } else {
        this.selectedNodes.push(clickedNode);
      }
      this.updatePropertiesPanel();
      return;
    }

    // 2) SHIFT + CLICK ON EMPTY SPACE → START BOX-SELECTION (NEW)
    if (!clickedNode && e.shiftKey) {
      e.preventDefault();
      this.isBoxSelecting = true;
      this.boxStartX = e.clientX - rect.left;
      this.boxStartY = e.clientY - rect.top;
      this.boxCurrentX = this.boxStartX;
      this.boxCurrentY = this.boxStartY;
      this.updatePropertiesPanel();
      return;
    }

    // 3) CTRL + CLICK ON A DIFFERENT NODE → connect first-selected → clickedNode
    if (this.isCtrlPressed && this.selectedNodes.length && clickedNode) {
      const fromId = this.selectedNodes[0].id;
      this.addConnection(fromId, clickedNode.id);
      this.recordHistory();
      return;
    }

    // 4a) CLICK ON GROUP TITLE → open group properties
    if (clickedGroupTitle && !clickedNode) {
      this.selectedGroup = clickedGroupTitle;
      this.openGroupProperties(clickedGroupTitle);
      return;
    }

    // 4b) CLICK ON GROUP RESIZE HANDLE → start resizing
    if (clickedGroupHandle && !clickedNode) {
      this.selectedGroup = clickedGroupHandle.group;
      this.isResizingGroup = true;
      this.resizingGroupHandle = clickedGroupHandle.handleName;
      this.draggedGroup = clickedGroupHandle.group;
      this.groupDragStartMouseWorldX = xWorld;
      this.groupDragStartMouseWorldY = yWorld;
      this.isDragging = true;
      return;
    }

    // 4c) CLICK ON A GROUP (no modifiers) → select and start dragging the group
    if (clickedGroup && !clickedNode && !clickedGroupTitle && !clickedGroupHandle) {
      this.selectedGroup = clickedGroup;
      this.isDraggingGroup = true;
      this.draggedGroup = clickedGroup;
      this.groupDragStartMouseWorldX = xWorld;
      this.groupDragStartMouseWorldY = yWorld;
      this.isDragging = true;
      return;
    }

    // 4b) CLICK ON A NODE (no shift, no ctrl)
    if (clickedNode) {
  // If this node wasn’t already in selectedNodes, select it
  const isInSelected = this.selectedNodes.some(n => n.id === clickedNode.id);
  if (!isInSelected) {
    this.selectedNodes = [clickedNode];
  }

  // THIS LINE is essential: it marks this node as “being dragged”
      // Check if we should do group drag instead
      if (isInSelected && this.selectedNodes.length > 1) {
        // Multi-node drag: set up group dragging
        this.draggedNodes = this.selectedNodes.slice();
        this.draggedNodesInitial = this.draggedNodes.map(n => ({ node: n, x: n.x, y: n.y }));
        this.groupDragStartMouseWorldX = xWorld;
        this.groupDragStartMouseWorldY = yWorld;
        this.draggedNode = null; // Clear single drag
        this.isDragging = true;
      } else {
        // Single node drag
        this.draggedNode = clickedNode;
        this.isDragging = true;
      }

      this.updatePropertiesPanel();

      // ─── EMIT NODE SELECTION EVENT FOR MULTIPLAYER ─────────────
      if (this.socket && this.isMultiplayerMode) {
        this.socket.emit('select-node', {
          nodeId: clickedNode.id,
          userColor: this.myCursorColor || '#00ff00'
        });
      }
      return;
    }

    // 5) CLICK ON EMPTY SPACE (no modifiers) → clear selection & start panning
    {
      if (this.socket && this.isMultiplayerMode && this.selectedNodes.length > 0) {
        this.socket.emit('deselect-node', {});
      }
      this.selectedNodes = [];
      this.draggedNode   = null;
      this.updatePropertiesPanel();

      this.isPanning = true;
      this.lastPanX = e.clientX;
      this.lastPanY = e.clientY;
      document.getElementById('canvasContainer').classList.add('panning');
      return;
    }
  }

  // ——————————————————————————————————————————————
  //  C) Any other button: do nothing
  // ——————————————————————————————————————————————
}

  /**
 * handleMouseMove: 
 *   • If BOX-SELECTING, update rectangle and re-render.
 *   • Otherwise, handle group dragging, single-node dragging, or panning as before.
 */
handleMouseMove(e) {
   console.log('>> handleMouseMove fired; isDragging=', this.isDragging, ' draggedNode=', this.draggedNode);

  const rect = this.canvas.getBoundingClientRect();
  const mouseX = e.clientX - rect.left;
  const mouseY = e.clientY - rect.top;

  // Store current mouse position in world coordinates for coordinate display and hotkeys
  const xWorld = (mouseX - this.offsetX) / this.scale;
  const yWorld = (mouseY - this.offsetY) / this.scale;
  this.lastMouseWorldX = xWorld;
  this.lastMouseWorldY = yWorld;
  this.currentMouseWorldX = xWorld;
  this.currentMouseWorldY = yWorld;

  // ─────────────────────────────────────────────────────────
  //  A) If we’re currently box-selecting (Shift+down on empty),
  //     update the rubber-band box’s “current” position.
  // ─────────────────────────────────────────────────────────
  if (this.isBoxSelecting) {
    // Update the current corner (screen space)
    this.boxCurrentX = mouseX;
    this.boxCurrentY = mouseY;

    // Re-render immediately so user sees the new rectangle
    this.render();
    return;
  }

  // ─────────────────────────────────────────────────────────
  //  B) World coordinates already calculated above
  // ─────────────────────────────────────────────────────────

  // ─────────────────────────────────────────────────────────
  //  C) If panning is true, warp the view
  // ─────────────────────────────────────────────────────────
  if (this.isPanning) {
    const dx = e.clientX - this.lastPanX;
    const dy = e.clientY - this.lastPanY;
    this.offsetX += dx;
    this.offsetY += dy;
    this.lastPanX = e.clientX;
    this.lastPanY = e.clientY;
    this.render();
    return;
  }

  // ─────────────────────────────────────────────────────────
  //  D) If we are dragging a story group container
  // ─────────────────────────────────────────────────────────
  if (this.isDraggingGroup && this.draggedGroup) {
    const deltaX = xWorld - this.groupDragStartMouseWorldX;
    const deltaY = yWorld - this.groupDragStartMouseWorldY;

    // Move the group container
    this.draggedGroup.x += deltaX;
    this.draggedGroup.y += deltaY;

    // Move all child nodes with the group
    this.draggedGroup.childNodeIDs.forEach(nodeId => {
      const node = this.nodes.find(n => n.id === nodeId);
      if (node) {
        node.x += deltaX;
        node.y += deltaY;
      }
    });

    // Update drag handles
    this.draggedGroup.dragHandles = this.createGroupDragHandles(
      this.draggedGroup.x,
      this.draggedGroup.y,
      this.draggedGroup.width,
      this.draggedGroup.height
    );

    // Update start position for next frame
    this.groupDragStartMouseWorldX = xWorld;
    this.groupDragStartMouseWorldY = yWorld;

    // Broadcast group movement to other users
    if (this.socket && this.isMultiplayerMode) {
      this.socket.emit('group-moved', {
        groupID: this.draggedGroup.id,
        deltaX: deltaX,
        deltaY: deltaY,
        newX: this.draggedGroup.x,
        newY: this.draggedGroup.y,
        movedBy: this.socket.id
      });
    }

    // Only render if significant movement to reduce refresh rate
    if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
      this.render();
    }
    return;
  }

  // ─────────────────────────────────────────────────────────
  //  E) If we are resizing a story group
  // ─────────────────────────────────────────────────────────
  if (this.isResizingGroup && this.draggedGroup && this.resizingGroupHandle) {
    const deltaX = xWorld - this.groupDragStartMouseWorldX;
    const deltaY = yWorld - this.groupDragStartMouseWorldY;

    this.resizeGroup(this.draggedGroup, this.resizingGroupHandle, deltaX, deltaY);

    // Update start position for next frame
    this.groupDragStartMouseWorldX = xWorld;
    this.groupDragStartMouseWorldY = yWorld;

    // Only render if significant movement to reduce refresh rate
    if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
      this.render();
    }
    return;
  }

  // ─────────────────────────────────────────────────────────
  //  F) If we are group-dragging (selectedNodes > 1),
  //     move all selected nodes
  // ─────────────────────────────────────────────────────────
  if (this.draggedNodes) {
    const curXWorld = xWorld;
    const curYWorld = yWorld;
    const deltaX = curXWorld - this.groupDragStartMouseWorldX;
    const deltaY = curYWorld - this.groupDragStartMouseWorldY;

    this.draggedNodes.forEach(item => {
      // Find the initial position for this node
      const initial = this.draggedNodesInitial.find(d => d.node.id === item.id);
      if (initial) {
        item.x = initial.x + deltaX;
        item.y = initial.y + deltaY;
      }
      // Actually, your original probably was `item.x = initialX + deltaX` 
      // Here’s the gist: restore to initial then add delta. For clarity:
      // item.x = this.draggedNodesInitial.find(d => d.node.id === item.id).x + deltaX;
      // item.y = this.draggedNodesInitial.find(d => d.node.id === item.id).y + deltaY;
      // (Use whichever form your code originally used.)
    });
    // Only render if significant movement to reduce refresh rate
    if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
      this.render();
    }
    return;
  }

  // ─────────────────────────────────────────────────────────
  //  E) If we’re dragging a single node
  // ─────────────────────────────────────────────────────────
  if (this.draggedNode) {
    const oldX = this.draggedNode.x;
    const oldY = this.draggedNode.y;
    this.draggedNode.x = xWorld;
    this.draggedNode.y = yWorld;

    // Only render if significant movement to reduce refresh rate
    if (Math.abs(xWorld - oldX) > 1 || Math.abs(yWorld - oldY) > 1) {
      this.render();
    }
    return;
  }

  // ─────────────────────────────────────────────────────────
  //  F) Otherwise, update remote‐cursor (if any) & do nothing else
  // ─────────────────────────────────────────────────────────
  if (this.socket) {
    // Throttle cursor updates to ~60fps
    const now = Date.now();
    if (now - this.lastCursorEmitTime >= this.cursorThrottleInterval) {
      const color = this.myCursorColor || (this.myCursorColor = '#00ff00');
      this.socket.emit('cursor-move', {
        x: xWorld,
        y: yWorld,
        color: color,
        name: this.userName
      });
      this.lastCursorEmitTime = now;
    }
  }

  // ─────────────────────────────────────────────────────────
  //  G) Broadcast box selection if active
  // ─────────────────────────────────────────────────────────
  if (this.isBoxSelecting && this.socket && this.isMultiplayerMode) {
    // Convert screen coordinates to world coordinates for broadcasting
    const x1World = (this.boxStartX - this.offsetX) / this.scale;
    const y1World = (this.boxStartY - this.offsetY) / this.scale;
    const x2World = (this.boxCurrentX - this.offsetX) / this.scale;
    const y2World = (this.boxCurrentY - this.offsetY) / this.scale;

    this.socket.emit('box-selecting', {
      x1: x1World,
      y1: y1World,
      x2: x2World,
      y2: y2World,
      userID: this.socket.id,
      userColor: this.myCursorColor || '#00ff00'
    });
  }
}

/**
 * handleMouseUp:
 *   • If BOX-SELECTING was in progress, compute which nodes fell inside
 *     that rectangle, add them to selectedNodes, then exit box mode.
 *   • Else if we were group-dragging, finalize history (existing).
 *   • Else if we were single-node dragging, finalize history (existing).
 */
 handleMouseUp(e) {
  console.log('>> handleMouseUp fired; draggedNode before clear =', this.draggedNode);

  const rect = this.canvas.getBoundingClientRect();
  const mouseX = e.clientX - rect.left;
  const mouseY = e.clientY - rect.top;

  // ─────────────────────────────────────────────────────────────
  //  A) If we released the mouse while BOX-SELECTING → finalize it
  // ─────────────────────────────────────────────────────────────
  if (this.isBoxSelecting) {
    // Compute the two corners of the box in SCREEN coordinates
    const x1 = this.boxStartX;
    const y1 = this.boxStartY;
    const x2 = this.boxCurrentX;
    const y2 = this.boxCurrentY;

    // Convert those corners back into WORLD coordinates
    // (because nodes are stored in world-space x/y).
    const worldX1 = (x1 - this.offsetX) / this.scale;
    const worldY1 = (y1 - this.offsetY) / this.scale;
    const worldX2 = (x2 - this.offsetX) / this.scale;
    const worldY2 = (y2 - this.offsetY) / this.scale;

    // Determine normalized min/max in world space
    const xMin = Math.min(worldX1, worldX2);
    const xMax = Math.max(worldX1, worldX2);
    const yMin = Math.min(worldY1, worldY2);
    const yMax = Math.max(worldY1, worldY2);

    // Find all nodes whose (node.x, node.y) lies inside [xMin, xMax] × [yMin, yMax].
    const nodesInBox = this.nodes.filter(node => {
      return node.x >= xMin && node.x <= xMax
          && node.y >= yMin && node.y <= yMax;
    });

    // ADDITIVELY add them into selectedNodes (skip duplicates)
    nodesInBox.forEach(node => {
      const already = this.selectedNodes.some(n => n.id === node.id);
      if (!already) {
        this.selectedNodes.push(node);
      }
    });

    // Exit box-select mode
    this.isBoxSelecting = false;

    // Broadcast box selection completion
    if (this.socket && this.isMultiplayerMode) {
      const selectedNodeIDs = nodesInBox.map(node => node.id);
      this.socket.emit('box-select-complete', {
        selectedNodeIDs: selectedNodeIDs,
        userID: this.socket.id
      });
    }

    // (Optional) reset box coordinates:
    this.boxStartX = this.boxStartY = this.boxCurrentX = this.boxCurrentY = 0;

    // Update UI & re-render
    this.updatePropertiesPanel();
    this.render();
    return;
  }

  // ─────────────────────────────────────────────────────────────
  //  B) If we were dragging a story group → finish
  // ─────────────────────────────────────────────────────────────
  if (this.isDraggingGroup && this.draggedGroup) {
    this.isDraggingGroup = false;
    this.draggedGroup = null;
    this.isDragging = false;
    this.recordHistory();
    return;
  }

  // ─────────────────────────────────────────────────────────────
  //  B2) If we were resizing a story group → finish
  // ─────────────────────────────────────────────────────────────
  if (this.isResizingGroup && this.draggedGroup) {
    this.isResizingGroup = false;
    this.resizingGroupHandle = null;
    this.draggedGroup = null;
    this.isDragging = false;
    this.recordHistory();
    return;
  }

  // ─────────────────────────────────────────────────────────────
  //  C) If we were group-dragging → finish & record history
  // ─────────────────────────────────────────────────────────────
  if (this.draggedNodes) {
    this.draggedNodes = null;
    this.draggedNodesInitial = [];
    this.isDragging = false; // Clear drag state
    this.processQueuedEvents(); // Process any queued remote events
    this.recordHistory();
    return;
  }

  // ─────────────────────────────────────────────────────────────
  //  C) If we were dragging one node → finish & record history
  // ─────────────────────────────────────────────────────────────
  if (this.draggedNode) {
    this.draggedNode = null;
    this.isDragging = false; // Clear drag state
    this.processQueuedEvents(); // Process any queued remote events
    this.recordHistory();
    return;
  }

  // ─────────────────────────────────────────────────────────────
  //  D) Otherwise (possible panning), let global handler catch it
  // ─────────────────────────────────────────────────────────────
}



  handleGlobalMouseMove(e) {
    if (this.isPanning && !this.draggedNode && !this.draggedNodes) {
      const deltaX = e.clientX - this.lastPanX;
      const deltaY = e.clientY - this.lastPanY;
      this.offsetX += deltaX;
      this.offsetY += deltaY;
      this.lastPanX = e.clientX;
      this.lastPanY = e.clientY;
      this.render();
    }
  }

  handleGlobalMouseUp(e) {
    this.isPanning = false;
    document.getElementById('canvasContainer').classList.remove('panning');
  }

  // ─────────────────────────────────────────────────────────────────
  // QUEUED EVENTS PROCESSING
  // ─────────────────────────────────────────────────────────────────

  processQueuedEvents() {
    // Process all queued remote events that were delayed during drag operations
    while (this.queuedRemoteEvents.length > 0) {
      const event = this.queuedRemoteEvents.shift();

      switch (event.type) {
        case 'full-state':
          this.nodes = JSON.parse(JSON.stringify(event.data.nodes));
          this.connections = JSON.parse(JSON.stringify(event.data.connections));
          break;

        case 'story-update':
          this.nodes = JSON.parse(JSON.stringify(event.data.nodes));
          this.connections = JSON.parse(JSON.stringify(event.data.connections));
          break;

        case 'node-created':
          const newNode = {
            id: event.data.nodeID,
            type: event.data.type,
            x: event.data.x,
            y: event.data.y,
            title: event.data.title,
            description: '',
            probability: event.data.type === 'start' ? 100 : 50,
            requirements: [],
            outcomes: [],
            metadata: {},
            skillEffects: [],
            nodeSkillRequirements: [],
            connectionSkillRequirements: {},
            faction: event.data.type === 'start' ? 'Pirates' : null,
            traits: [],
            factionRelationshipModifiers: {}
          };
          this.nodes.push(newNode);
          break;

        case 'connection-removed':
          this.connections = this.connections.filter(conn =>
            !(conn.from === event.data.from && conn.to === event.data.to)
          );
          break;

        case 'connection-label-changed':
          const conn = this.connections.find(c =>
            c.from === event.data.connectionID.from && c.to === event.data.connectionID.to
          );
          if (conn) {
            conn.label = event.data.newLabel;
          }
          break;
      }
    }

    // Update UI after processing all queued events
    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  }

  // Zoom-to-cursor
  handleWheel(e) {
    e.preventDefault();
    const rect = this.canvas.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const oldScale = this.scale;
    const scaleFactor = e.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.max(0.1, Math.min(3, oldScale * scaleFactor));

    const worldX = (mouseX - this.offsetX) / oldScale;
    const worldY = (mouseY - this.offsetY) / oldScale;

    this.scale = newScale;
    this.offsetX = mouseX - worldX * newScale;
    this.offsetY = mouseY - worldY * newScale;

    this.render();
  }

  // Handle right-click context menu
  handleContextMenu(e) {
    e.preventDefault();

    // Check if Shift is held and we have selected nodes
    if (e.shiftKey && this.selectedNodes.length > 0) {
      this.createStoryGroup();
    }
  }

  // Create a story group from selected nodes
  createStoryGroup() {
    if (this.selectedNodes.length === 0) return;

    const groupId = this.nextGroupId++;
    const selectedNodeIds = this.selectedNodes.map(node => node.id);

    // Calculate bounding box for the group with padding
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    this.selectedNodes.forEach(node => {
      minX = Math.min(minX, node.x - 40);
      minY = Math.min(minY, node.y - 40);
      maxX = Math.max(maxX, node.x + 40);
      maxY = Math.max(maxY, node.y + 40);
    });

    const group = {
      id: groupId,
      name: `Group ${groupId}`,
      color: this.myCursorColor || '#4CAF50',
      childNodeIDs: selectedNodeIds,
      x: minX,
      y: minY - 30, // Extra space for title
      width: maxX - minX,
      height: maxY - minY + 30,
      createdBy: this.socket?.id || 'local',
      isEditing: false,
      dragHandles: this.createGroupDragHandles(minX, minY - 30, maxX - minX, maxY - minY + 30)
    };

    this.storyGroups.push(group);

    // Broadcast group creation if in multiplayer
    if (this.socket && this.isMultiplayerMode) {
      this.socket.emit('group-created', {
        groupID: groupId,
        name: group.name,
        color: group.color,
        childNodeIDs: selectedNodeIds,
        createdBy: this.socket.id
      });
    }

    this.render();
    this.showToast(`Created story group: ${group.name}`, 'success');
  }

  // Show toast notification
  showToast(message, type = 'info') {
    // Create toast element if it doesn't exist
    let toast = document.getElementById('toast-notification');
    if (!toast) {
      toast = document.createElement('div');
      toast.id = 'toast-notification';
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        opacity: 0;
        transition: opacity 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
      `;
      document.body.appendChild(toast);
    }

    // Set message and style based on type
    toast.textContent = message;
    switch (type) {
      case 'success':
        toast.style.backgroundColor = '#4CAF50';
        break;
      case 'error':
        toast.style.backgroundColor = '#f44336';
        break;
      case 'warning':
        toast.style.backgroundColor = '#FF9800';
        break;
      default:
        toast.style.backgroundColor = '#2196F3';
    }

    // Show toast
    toast.style.opacity = '1';

    // Hide after 3 seconds
    setTimeout(() => {
      toast.style.opacity = '0';
    }, 3000);
  }

  // Create drag handles for group resizing
  createGroupDragHandles(x, y, width, height) {
    const handleSize = 8;
    return {
      topLeft: { x: x - handleSize/2, y: y - handleSize/2, width: handleSize, height: handleSize },
      topRight: { x: x + width - handleSize/2, y: y - handleSize/2, width: handleSize, height: handleSize },
      bottomLeft: { x: x - handleSize/2, y: y + height - handleSize/2, width: handleSize, height: handleSize },
      bottomRight: { x: x + width - handleSize/2, y: y + height - handleSize/2, width: handleSize, height: handleSize },
      top: { x: x + width/2 - handleSize/2, y: y - handleSize/2, width: handleSize, height: handleSize },
      bottom: { x: x + width/2 - handleSize/2, y: y + height - handleSize/2, width: handleSize, height: handleSize },
      left: { x: x - handleSize/2, y: y + height/2 - handleSize/2, width: handleSize, height: handleSize },
      right: { x: x + width - handleSize/2, y: y + height/2 - handleSize/2, width: handleSize, height: handleSize }
    };
  }

  // Resize group based on handle being dragged
  resizeGroup(group, handleName, deltaX, deltaY) {
    const minSize = 50; // Minimum group size

    switch (handleName) {
      case 'topLeft':
        group.x += deltaX;
        group.y += deltaY;
        group.width -= deltaX;
        group.height -= deltaY;
        break;
      case 'topRight':
        group.y += deltaY;
        group.width += deltaX;
        group.height -= deltaY;
        break;
      case 'bottomLeft':
        group.x += deltaX;
        group.width -= deltaX;
        group.height += deltaY;
        break;
      case 'bottomRight':
        group.width += deltaX;
        group.height += deltaY;
        break;
      case 'top':
        group.y += deltaY;
        group.height -= deltaY;
        break;
      case 'bottom':
        group.height += deltaY;
        break;
      case 'left':
        group.x += deltaX;
        group.width -= deltaX;
        break;
      case 'right':
        group.width += deltaX;
        break;
    }

    // Enforce minimum size
    if (group.width < minSize) {
      group.width = minSize;
    }
    if (group.height < minSize) {
      group.height = minSize;
    }

    // Update drag handles
    group.dragHandles = this.createGroupDragHandles(group.x, group.y, group.width, group.height);
  }

  // Open group properties panel
  openGroupProperties(group) {
    this.selectedGroup = group;
    this.updatePropertiesPanel();
  }

  // Update group property
  updateGroupProperty(property, value) {
    if (!this.selectedGroup) return;

    this.selectedGroup[property] = value;
    this.markUnsaved();
    this.render();

    // Broadcast group update to other users
    if (this.socket && this.isMultiplayerMode) {
      this.socket.emit('group-updated', {
        groupID: this.selectedGroup.id,
        property: property,
        value: value,
        updatedBy: this.socket.id
      });
    }
  }

  // Show group properties in the properties panel
  showGroupProperties() {
    const panel = document.getElementById('nodeProperties');
    const group = this.selectedGroup;

    if (!group) return;

    panel.innerHTML = `
      <div class="form-section">
        <h4>📦 Group Properties</h4>
        <div class="form-group">
          <label>Group Name:</label>
          <input type="text" value="${group.name || ''}" onchange="storyBuilder.updateGroupProperty('name', this.value)">
        </div>
        <div class="form-group">
          <label>Description:</label>
          <textarea rows="3" onchange="storyBuilder.updateGroupProperty('description', this.value)" placeholder="Describe this group...">${group.description || ''}</textarea>
        </div>
        <div class="form-group">
          <label>Group Color:</label>
          <input type="color" value="${group.color || '#4CAF50'}" onchange="storyBuilder.updateGroupProperty('color', this.value)">
        </div>
        <div class="form-group">
          <label>Position:</label>
          <div class="input-group">
            <input type="number" value="${Math.round(group.x)}" onchange="storyBuilder.updateGroupProperty('x', parseFloat(this.value))" placeholder="X">
            <input type="number" value="${Math.round(group.y)}" onchange="storyBuilder.updateGroupProperty('y', parseFloat(this.value))" placeholder="Y">
          </div>
        </div>
        <div class="form-group">
          <label>Size:</label>
          <div class="input-group">
            <input type="number" value="${Math.round(group.width)}" onchange="storyBuilder.updateGroupProperty('width', parseFloat(this.value))" placeholder="Width">
            <input type="number" value="${Math.round(group.height)}" onchange="storyBuilder.updateGroupProperty('height', parseFloat(this.value))" placeholder="Height">
          </div>
        </div>
        <div class="form-group">
          <label>Child Nodes:</label>
          <div style="background: rgba(255,255,255,0.1); padding: 10px; border-radius: 4px; max-height: 150px; overflow-y: auto;">
            ${group.childNodeIDs.map(nodeId => {
              const node = this.nodes.find(n => n.id === nodeId);
              return node ? `<div style="margin: 2px 0; padding: 4px; background: rgba(255,255,255,0.1); border-radius: 2px;">${node.title} (ID: ${nodeId})</div>` : '';
            }).join('')}
          </div>
        </div>
        <div class="form-group">
          <label>Created By:</label>
          <input type="text" value="${group.createdBy || 'Unknown'}" readonly style="background: rgba(255,255,255,0.1);">
        </div>
      </div>

      <div class="form-section">
        <h4>🎛️ Group Actions</h4>
        <button class="btn btn-warning" onclick="storyBuilder.deleteGroup(${group.id})" style="width: 100%; margin-bottom: 10px;">
          🗑️ Delete Group
        </button>
        <button class="btn btn-secondary" onclick="storyBuilder.selectedGroup = null; storyBuilder.updatePropertiesPanel();" style="width: 100%;">
          ❌ Deselect Group
        </button>
      </div>
    `;
  }

  // Delete a group
  deleteGroup(groupId) {
    if (!confirm('Are you sure you want to delete this group?')) return;

    this.storyGroups = this.storyGroups.filter(g => g.id !== groupId);
    this.selectedGroup = null;
    this.updatePropertiesPanel();
    this.render();
    this.markUnsaved();

    // Broadcast group deletion
    if (this.socket && this.isMultiplayerMode) {
      this.socket.emit('group-deleted', {
        groupID: groupId,
        deletedBy: this.socket.id
      });
    }

    this.showToast('Group deleted', 'info');
  }

  // Auto-attach nodes to groups based on position and size
  autoAttachNodesToGroups() {
    if (!this.storyGroups || this.storyGroups.length === 0) return;

    this.storyGroups.forEach(group => {
      // Clear existing child node IDs
      group.childNodeIDs = [];

      // Find nodes that are within this group's bounds
      this.nodes.forEach(node => {
        if (this.isNodeInGroup(node, group)) {
          group.childNodeIDs.push(node.id);
        }
      });

      // Recreate drag handles for the group
      group.dragHandles = this.createGroupDragHandles(group.x, group.y, group.width, group.height);
    });

    console.log('Auto-attached nodes to groups:', this.storyGroups.map(g => ({
      groupId: g.id,
      name: g.name,
      childCount: g.childNodeIDs.length
    })));
  }

  // Check if a node is within a group's bounds
  isNodeInGroup(node, group) {
    const nodeRadius = 30; // Node visual radius
    const nodeCenterX = node.x;
    const nodeCenterY = node.y;

    // Check if node center is within group bounds (with some padding)
    return nodeCenterX >= group.x - nodeRadius &&
           nodeCenterX <= group.x + group.width + nodeRadius &&
           nodeCenterY >= group.y - nodeRadius &&
           nodeCenterY <= group.y + group.height + nodeRadius;
  }

  // Initialize periodic render for multiplayer synchronization
  initializePeriodicRender() {
    // Only render periodically if in multiplayer mode and not actively dragging
    setInterval(() => {
      if (this.isMultiplayerMode && !this.isDragging && !this.isPanning) {
        // Check if there are any remote cursors or selections that need updating
        const hasRemoteActivity = Object.keys(this.remoteCursors || {}).length > 0 ||
                                 Object.keys(this.remoteSelections || {}).length > 0 ||
                                 Object.keys(this.remoteBoxSelections || {}).length > 0;

        if (hasRemoteActivity) {
          this.render();
        }
      }
    }, 100); // Update every 100ms when there's remote activity
  }

  // Handle connection label hotkeys (Ctrl+1 through Ctrl+9)
  handleConnectionLabelHotkey(labelIndex) {
    // Find connection under mouse cursor
    const rect = this.canvas.getBoundingClientRect();
    const mouseX = this.lastMouseWorldX;
    const mouseY = this.lastMouseWorldY;

    // Find the closest connection to the mouse cursor
    let closestConnection = null;
    let minDistance = Infinity;

    this.connections.forEach(conn => {
      const fromNode = this.nodes.find(n => n.id === conn.from);
      const toNode = this.nodes.find(n => n.id === conn.to);

      if (fromNode && toNode) {
        // Calculate distance from mouse to connection line
        const distance = this.distanceToLine(mouseX, mouseY, fromNode.x, fromNode.y, toNode.x, toNode.y);
        if (distance < minDistance && distance < 20) { // Within 20 pixels
          minDistance = distance;
          closestConnection = conn;
        }
      }
    });

    if (closestConnection && labelIndex < this.CONNECTION_LABELS.length) {
      const newLabel = this.CONNECTION_LABELS[labelIndex];
      this.updateConnectionProperty(closestConnection.from, closestConnection.to, 'label', newLabel);
      this.showToast(`Connection label set to: ${newLabel}`, 'info');
    }
  }

  // Calculate distance from point to line segment
  distanceToLine(px, py, x1, y1, x2, y2) {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;

    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;
    if (lenSq !== 0) {
      param = dot / lenSq;
    }

    let xx, yy;
    if (param < 0) {
      xx = x1;
      yy = y1;
    } else if (param > 1) {
      xx = x2;
      yy = y2;
    } else {
      xx = x1 + param * C;
      yy = y1 + param * D;
    }

    const dx = px - xx;
    const dy = py - yy;
    return Math.sqrt(dx * dx + dy * dy);
  }

  // Open connection editing window
  openConnectionEditWindow(connection) {
    const fromNode = this.nodes.find(n => n.id === connection.from);
    const toNode = this.nodes.find(n => n.id === connection.to);

    if (!fromNode || !toNode) return;

    // Select the from node to show its connections in the properties panel
    this.selectedNodes = [fromNode];
    this.updatePropertiesPanel();

    // Scroll to the connection in the properties panel
    setTimeout(() => {
      const connectionElement = document.querySelector(`[data-connection-from="${connection.from}"][data-connection-to="${connection.to}"]`);
      if (connectionElement) {
        connectionElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        connectionElement.style.backgroundColor = 'rgba(33, 150, 243, 0.3)';
        setTimeout(() => {
          connectionElement.style.backgroundColor = '';
        }, 2000);
      }
    }, 100);

    this.showToast(`Editing connection: ${fromNode.title} → ${toNode.title}`, 'info');
  }

  // Start dragging a new node from the palette
  startNodeDrag(nodeType, event) {
    this.isDraggingNewNode = true;
    this.newNodeType = nodeType;

    // Create a ghost node that follows the cursor
    this.ghostNode = {
      type: nodeType,
      x: 0,
      y: 0
    };

    // Add global mouse move and up listeners
    document.addEventListener('mousemove', this.handleNodeDragMove.bind(this));
    document.addEventListener('mouseup', this.handleNodeDragEnd.bind(this));

    console.log('Started dragging new node:', nodeType);
  }

  // Handle mouse move during node drag
  handleNodeDragMove(e) {
    if (!this.isDraggingNewNode || !this.ghostNode) return;

    const rect = this.canvas.getBoundingClientRect();
    const xWorld = (e.clientX - rect.left - this.offsetX) / this.scale;
    const yWorld = (e.clientY - rect.top - this.offsetY) / this.scale;

    this.ghostNode.x = xWorld;
    this.ghostNode.y = yWorld;

    this.render(); // Re-render to show ghost node
  }

  // Handle mouse up during node drag
  handleNodeDragEnd(e) {
    if (!this.isDraggingNewNode) return;

    // Remove global listeners
    document.removeEventListener('mousemove', this.handleNodeDragMove.bind(this));
    document.removeEventListener('mouseup', this.handleNodeDragEnd.bind(this));

    // Check if we're over the canvas
    const rect = this.canvas.getBoundingClientRect();
    const isOverCanvas = e.clientX >= rect.left && e.clientX <= rect.right &&
                        e.clientY >= rect.top && e.clientY <= rect.bottom;

    if (isOverCanvas && this.ghostNode) {
      // Create the node at the ghost position
      this.addNode(this.newNodeType, this.ghostNode.x, this.ghostNode.y);
    }

    // Clean up
    this.isDraggingNewNode = false;
    this.newNodeType = null;
    this.ghostNode = null;

    this.render(); // Re-render to remove ghost node
  }

  // ─────────────────────────────────────────────────────────────────
  // AUTOSAVE
  // ─────────────────────────────────────────────────────────────────

  setupAutosave() {
    this.autosaveInterval = setInterval(() => {
      this.performAutosave();
    }, 60000); // every 60 seconds

    this.autosaveTimeout = null;
  }

  async performAutosave() {
    if (!this.hasUnsavedChanges) return;
    try {
      const indicator = document.getElementById('autosaveIndicator');
      const text = document.getElementById('autosaveText');
      indicator.classList.add('saving');
      text.textContent = '💾 Saving...';

      const autoSaveName = this.currentStoryName ||
        `autosave_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}`;
      const storyData = {
        nodes: this.nodes,
        connections: this.connections,
        groups: this.storyGroups || [],
        metadata: {
          lastModified: Date.now(),
          version: "1.4"
        }
      };
      await this.database.saveStory(autoSaveName, storyData);
      this.lastAutosave = Date.now();
      this.markSaved();

      text.textContent = '💾 Autosaved';
      indicator.classList.remove('saving');
      setTimeout(() => {
        if (!this.hasUnsavedChanges) {
          text.textContent = '💾 Autosaved';
        }
      }, 2000);
    } catch (error) {
      console.error('Autosave failed:', error);
      document.getElementById('autosaveText').textContent = '⚠️ Save failed';
    }
  }

  markUnsaved() {
    this.hasUnsavedChanges = true;
    document.getElementById('unsavedIndicator').style.display = 'block';
    clearTimeout(this.autosaveTimeout);
    this.autosaveTimeout = setTimeout(() => {
      this.performAutosave();
    }, 5000);
  }

  markSaved() {
    this.hasUnsavedChanges = false;
    document.getElementById('unsavedIndicator').style.display = 'none';
    clearTimeout(this.autosaveTimeout);
  }

  // ─────────────────────────────────────────────────────────────────
  // NODE & CONNECTION MANAGEMENT
  // ─────────────────────────────────────────────────────────────────

  // Generate a unique node ID that doesn't conflict with existing nodes
  generateUniqueNodeId() {
    let candidateId = this.nextNodeId;
    while (this.nodes.some(node => node.id === candidateId)) {
      candidateId++;
    }
    this.nextNodeId = candidateId + 1;
    return candidateId;
  }

  // Fix any duplicate node IDs in the current story
  fixDuplicateNodeIds() {
    const seenIds = new Set();
    const idMapping = {};
    let hasChanges = false;

    // First pass: identify duplicates and create new IDs
    this.nodes.forEach(node => {
      if (seenIds.has(node.id)) {
        // This is a duplicate - generate a new unique ID
        const newId = this.generateUniqueNodeId();
        idMapping[node.id] = newId;
        node.id = newId;
        hasChanges = true;
        console.log(`Fixed duplicate node ID: ${node.id} -> ${newId}`);
      } else {
        seenIds.add(node.id);
      }
    });

    // Second pass: update connections that reference the changed IDs
    if (hasChanges) {
      this.connections.forEach(conn => {
        if (idMapping[conn.from]) {
          conn.from = idMapping[conn.from];
        }
        if (idMapping[conn.to]) {
          conn.to = idMapping[conn.to];
        }
      });

      // Update nextNodeId to be higher than all existing IDs
      this.nextNodeId = Math.max(...this.nodes.map(n => n.id)) + 1;

      this.showToast(`Fixed ${Object.keys(idMapping).length} duplicate node IDs`, 'info');
    }

    return hasChanges;
  }

  addNode(type, x, y) {
    // In multiplayer mode, request server-assigned ID
    if (this.socket && this.isMultiplayerMode) {
      // Increment the counter for this specific node type
      this.nodeTypeCounters[type] = (this.nodeTypeCounters[type] || 0) + 1;

      this.socket.emit('request-new-node', {
        type: type,
        x: x,
        y: y,
        createdBy: this.socket.id,
        title: `${this.nodeTypes[type].label} ${this.nodeTypeCounters[type]}`
      });
      return; // Server will respond with node-created event
    }

    // Single-player mode - use local ID generation with uniqueness check
    this.nodeTypeCounters[type] = (this.nodeTypeCounters[type] || 0) + 1;

    const node = {
      id: this.generateUniqueNodeId(),
      type: type,
      x: x,
      y: y,
      title: `${this.nodeTypes[type].label} ${this.nodeTypeCounters[type]}`,
      description: '',
      probability: type === 'start' ? 100 : 50,
      requirements: [],
      outcomes: [],
      metadata: {},
      skillEffects: [],
      nodeSkillRequirements: [],
      connectionSkillRequirements: {},
      faction: type === 'start' ? 'Pirates' : null,
      traits: [],
      factionRelationshipModifiers: {}
    };

    this.nodes.push(node);
    this.markUnsaved();
    this.recordHistory();
    this.updateStats();
    this.render();
    return node;
  }

  deleteNode(node) {
    this.connections = this.connections.filter(conn =>
      conn.from !== node.id && conn.to !== node.id
    );
    this.nodes = this.nodes.filter(n => n.id !== node.id);

    if (this.selectedNodes.some(n => n.id === node.id)) {
      this.selectedNodes = [];
      this.updatePropertiesPanel();
    }

    this.markUnsaved();
    this.recordHistory();
    this.updateStats();
    this.render();
  }

  deleteSelectedNodes() {
    if (this.selectedNodes.length === 0) return;

    // Get all node IDs to delete
    const nodeIdsToDelete = this.selectedNodes.map(node => node.id);

    // Remove all connections that involve any of these nodes
    this.connections = this.connections.filter(conn =>
      !nodeIdsToDelete.includes(conn.from) && !nodeIdsToDelete.includes(conn.to)
    );

    // Remove all selected nodes
    this.nodes = this.nodes.filter(node => !nodeIdsToDelete.includes(node.id));

    // Clear selection
    this.selectedNodes = [];

    this.markUnsaved();
    this.recordHistory();
    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  }

  getNodeAt(x, y) {
    // Check nodes in reverse order (top to bottom) to handle stacked nodes correctly
    for (let i = this.nodes.length - 1; i >= 0; i--) {
      const node = this.nodes[i];
      const dx = x - node.x;
      const dy = y - node.y;
      if (Math.sqrt(dx * dx + dy * dy) < 30) {
        return node;
      }
    }
    return null;
  }

  getGroupAt(x, y) {
    // Check groups in reverse order (top to bottom) to handle overlapping groups correctly
    if (!this.storyGroups) return null;

    for (let i = this.storyGroups.length - 1; i >= 0; i--) {
      const group = this.storyGroups[i];
      if (x >= group.x && x <= group.x + group.width &&
          y >= group.y && y <= group.y + group.height) {
        return group;
      }
    }
    return null;
  }

  getGroupHandleAt(x, y) {
    // Check if click is on a group resize handle
    if (!this.storyGroups || !this.selectedGroup) return null;

    const group = this.selectedGroup;
    if (!group.dragHandles) return null;

    for (const [handleName, handle] of Object.entries(group.dragHandles)) {
      if (x >= handle.x && x <= handle.x + handle.width &&
          y >= handle.y && y <= handle.y + handle.height) {
        return { group, handleName, handle };
      }
    }
    return null;
  }

  getGroupTitleAt(x, y) {
    // Check if click is on group title area for editing
    if (!this.storyGroups) return null;

    for (let i = this.storyGroups.length - 1; i >= 0; i--) {
      const group = this.storyGroups[i];
      // Title area is the top 30 pixels of the group
      if (x >= group.x && x <= group.x + group.width &&
          y >= group.y && y <= group.y + 30) {
        return group;
      }
    }
    return null;
  }

  addConnection(fromId, toId) {
    // Check if connection already exists (in either direction)
    const existingConnection = this.connections.find(conn =>
      (conn.from === fromId && conn.to === toId) ||
      (conn.from === toId && conn.to === fromId)
    );

    if (existingConnection) {
      // Connection exists - remove it instead
      this.removeConnection(existingConnection.from, existingConnection.to);
      return;
    }

    // No existing connection - create new one
    const connection = {
      from: fromId,
      to: toId,
      probability: 50,
      label: 'Connection',
      customLabel: '',
      condition: '',
      daysToNext: { type: 'fixed', value: 1, dice: '1d3' },
      skillRequirements: [],
      successTarget: null,
      failureTarget: null,
      faction: null,
      traits: [],
      factionProbabilityModifiers: {},
      relationshipModifier: 0
    };
    this.connections.push(connection);
    this.redistributeConnectionProbabilities(fromId);
    this.markUnsaved();
    this.recordHistory();
    this.updateStats();
    this.updatePropertiesPanel();
    this.render();
  }

  removeConnection(fromId, toId) {
    // Remove the connection locally
    this.connections = this.connections.filter(c =>
      !(c.from === fromId && c.to === toId)
    );

    // Broadcast removal to other clients
    if (this.socket && this.isMultiplayerMode) {
      this.socket.emit('remove-connection', {
        from: fromId,
        to: toId,
        removedBy: this.socket.id
      });
    }

    this.redistributeConnectionProbabilities(fromId);
    this.markUnsaved();
    this.recordHistory();
    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  }

  redistributeConnectionProbabilities(nodeId) {
    const outgoing = this.connections.filter(c => c.from === nodeId);
    if (outgoing.length === 0) return;
    if (outgoing.length === 1) {
      outgoing[0].probability = 100;
    } else {
      const equalShare = Math.floor(100 / outgoing.length);
      const remainder = 100 - equalShare * outgoing.length;
      outgoing.forEach((conn, i) => {
        conn.probability = equalShare + (i < remainder ? 1 : 0);
      });
    }
  }

  updateConnectionProbability(fromId, toId, newProb) {
    const conn = this.connections.find(c => c.from === fromId && c.to === toId);
    if (!conn) return;
    const outgoing = this.connections.filter(c => c.from === fromId);
    if (outgoing.length === 1) {
      conn.probability = 100;
      return;
    }
    conn.probability = Math.max(0, Math.min(100, newProb));
    const totalOthers = outgoing.filter(c => c !== conn)
      .reduce((sum, c) => sum + c.probability, 0);
    const remaining = 100 - conn.probability;
    if (totalOthers > 0 && remaining > 0) {
      const scale = remaining / totalOthers;
      outgoing.filter(c => c !== conn).forEach(c => {
        c.probability = Math.round(c.probability * scale);
      });
    }
    this.markUnsaved();
    this.recordHistory();
    this.render();
    this.updatePropertiesPanel();
  }

  updateConnectionProperty(fromId, toId, property, value) {
  // 1) Locate the connection
  const connection = this.connections.find(conn =>
    conn.from === fromId && conn.to === toId
  );
  if (!connection) return;

  // 2) Update the property
  connection[property] = value;

  // 3) Broadcast label changes in multiplayer
  if (property === 'label' && this.socket && this.isMultiplayerMode) {
    this.socket.emit('connection-label-changed', {
      connectionID: { from: fromId, to: toId },
      newLabel: value,
      changedBy: this.socket.id
    });
  }

  // 4) Mark unsaved + record history (for undo & broadcast)
  this.markUnsaved();
  this.recordHistory();

  // 5) Immediately re-render so the new color/label is visible
  this.render();

  // 6) Refresh the right-hand properties panel
  this.updatePropertiesPanel();
}


  updateConnectionDaysType(fromId, toId, type) {
    const conn = this.connections.find(c => c.from === fromId && c.to === toId);
    if (!conn) return;
    if (type === 'fixed') {
      conn.daysToNext = { type: 'fixed', value: 1 };
    } else {
      conn.daysToNext = { type: 'dice', dice: '1d3' };
    }
    this.markUnsaved();
    this.recordHistory();
    this.updatePropertiesPanel();
  }

  updateConnectionDaysValue(fromId, toId, value) {
    const conn = this.connections.find(c => c.from === fromId && c.to === toId);
    if (!conn) return;
    if (conn.daysToNext.type === 'fixed') {
      conn.daysToNext.value = parseInt(value) || 1;
    } else {
      conn.daysToNext.dice = value || '1d3';
    }
    this.markUnsaved();
    this.recordHistory();
    this.render();
  }

  updateConnectionRelationshipModifier(fromId, toId, modifier) {
    const conn = this.connections.find(c => c.from === fromId && c.to === toId);
    if (!conn) return;
    conn.relationshipModifier = parseInt(modifier) || 0;
    this.markUnsaved();
    this.recordHistory();
    this.updatePropertiesPanel();
  }

  deleteConnection(fromId, toId) {
    this.connections = this.connections.filter(c =>
      !(c.from === fromId && c.to === toId)
    );
    this.redistributeConnectionProbabilities(fromId);
    this.markUnsaved();
    this.recordHistory();
    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  }

  updateNodeProperty(property, value) {
    if (!this.selectedNodes.length) return;
    const node = this.selectedNodes[0];
    if (property === 'requirements' || property === 'metadata') {
      try {
        node[property] = JSON.parse(value);
      } catch (e) {
        console.error('Invalid JSON:', e);
        return;
      }
    } else {
      node[property] = value;
    }
    this.markUnsaved();
    this.recordHistory();
    this.render();
    this.updateStats();
  }

  // ─────────────────────────────────────────────────────────────────
  // PATH ESTIMATION & UTILITIES
  // ─────────────────────────────────────────────────────────────────

  calculatePathEstimation(nodeId) {
    const node = this.nodes.find(n => n.id === nodeId);
    if (!node) return { min: 0, max: 0 };
    if (node.type === 'start') return { min: 0, max: 0 };

    const visited = new Set();
    const memo = new Map();

    const findPathDays = (currentId, accumDays = { min: 0, max: 0 }) => {
      const memoKey = `${currentId}-${accumDays.min}-${accumDays.max}`;
      if (memo.has(memoKey)) return memo.get(memoKey);
      if (visited.has(currentId)) return { min: Infinity, max: Infinity };
      visited.add(currentId);

      const currentNode = this.nodes.find(n => n.id === currentId);
      if (!currentNode) {
        visited.delete(currentId);
        return { min: Infinity, max: Infinity };
      }
      if (currentId === nodeId) {
        visited.delete(currentId);
        memo.set(memoKey, accumDays);
        return accumDays;
      }

      const outgoing = this.connections.filter(c => c.from === currentId);
      if (outgoing.length === 0) {
        visited.delete(currentId);
        const result = { min: Infinity, max: Infinity };
        memo.set(memoKey, result);
        return result;
      }

      let minPath = Infinity;
      let maxPath = Infinity;

      outgoing.forEach(conn => {
        const dayDelay = this.parseDayDelay(conn.daysToNext);
        const newAccum = {
          min: accumDays.min + dayDelay.min,
          max: accumDays.max + dayDelay.max
        };
        const subPath = findPathDays(conn.to, newAccum);
        if (subPath.min < minPath) minPath = subPath.min;
        if (subPath.max < maxPath) maxPath = subPath.max;
      });

      visited.delete(currentId);
      const result = { min: minPath, max: maxPath };
      memo.set(memoKey, result);
      return result;
    };

    const startNodes = this.nodes.filter(n => n.type === 'start');
    let shortestMin = Infinity;
    let shortestMax = Infinity;

    startNodes.forEach(startNode => {
      visited.clear();
      const path = findPathDays(startNode.id);
      if (path.min < shortestMin) shortestMin = path.min;
      if (path.max < shortestMax) shortestMax = path.max;
    });

    return {
      min: shortestMin === Infinity ? 0 : shortestMin,
      max: shortestMax === Infinity ? 0 : shortestMax
    };
  }

  parseDayDelay(daysToNext) {
    if (daysToNext.type === 'fixed') {
      return { min: daysToNext.value, max: daysToNext.value };
    } else if (daysToNext.type === 'dice') {
      const match = daysToNext.dice.match(/(\d+)d(\d+)(?:([+-])(\d+))?/);
      if (match) {
        const count = parseInt(match[1]);
        const sides = parseInt(match[2]);
        const operator = match[3];
        const modifier = parseInt(match[4]) || 0;
        let min = count;
        let max = count * sides;
        if (operator === '+') {
          min += modifier;
          max += modifier;
        } else if (operator === '-') {
          min -= modifier;
          max -= modifier;
        }
        return { min: Math.max(0, min), max: Math.max(0, max) };
      }
    }
    return { min: 1, max: 1 };
  }

  // ─────────────────────────────────────────────────────────────────
  // SKILL & OUTCOME ANALYSIS
  // ─────────────────────────────────────────────────────────────────

  analyzeSkillUsage() {
    const skillUsage = {};
    this.nodes.forEach(node => {
      (node.nodeSkillRequirements || []).forEach(req => {
        if (!skillUsage[req.skill]) {
          skillUsage[req.skill] = { count: 0, totalWeight: 0 };
        }
        skillUsage[req.skill].count++;
        skillUsage[req.skill].totalWeight += req.weight || 1;
      });
      (node.skillEffects || []).forEach(effect => {
        if (!skillUsage[effect.skill]) {
          skillUsage[effect.skill] = { count: 0, totalWeight: 0 };
        }
        skillUsage[effect.skill].count++;
        skillUsage[effect.skill].totalWeight += 1;
      });
    });
    return Object.entries(skillUsage)
      .map(([skill, data]) => ({
        skill,
        usage: data.count,
        weight: data.totalWeight,
        score: data.count * data.totalWeight
      }))
      .sort((a, b) => b.score - a.score);
  }

  analyzeOutcomeProbabilities() {
    const outcomes = this.nodes.filter(n => n.type === 'outcome');
    const startNodes = this.nodes.filter(n => n.type === 'start');
    if (outcomes.length === 0 || startNodes.length === 0) return [];

    const outcomeProbabilities = {};
    outcomes.forEach(outcome => {
      outcomeProbabilities[outcome.id] = { node: outcome, probability: 0 };
    });

    const calculateOutcomeProbability = (nodeId, probability = 1, visited = new Set()) => {
      if (visited.has(nodeId) || probability <= 0) return;
      visited.add(nodeId);
      const node = this.nodes.find(n => n.id === nodeId);
      if (!node) {
        visited.delete(nodeId);
        return;
      }
      if (node.type === 'outcome') {
        outcomeProbabilities[nodeId].probability += probability;
        visited.delete(nodeId);
        return;
      }
      const outgoing = this.connections.filter(c => c.from === nodeId);
      if (outgoing.length === 0) {
        visited.delete(nodeId);
        return;
      }
      outgoing.forEach(conn => {
        const connProbability = (conn.probability / 100) * probability;
        calculateOutcomeProbability(conn.to, connProbability, new Set(visited));
      });
      visited.delete(nodeId);
    };

    startNodes.forEach(startNode => {
      calculateOutcomeProbability(startNode.id, 1);
    });

    const totalProb = Object.values(outcomeProbabilities)
      .reduce((sum, o) => sum + o.probability, 0);
    if (totalProb > 0) {
      Object.values(outcomeProbabilities).forEach(o => {
        o.probability = (o.probability / totalProb) * 100;
      });
    }

    return Object.values(outcomeProbabilities)
      .filter(o => o.probability > 0)
      .sort((a, b) => b.probability - a.probability);
  }

  // ─────────────────────────────────────────────────────────────────
  // UI UPDATES
  // ─────────────────────────────────────────────────────────────────

  updateStats() {
    document.getElementById('nodeCount').textContent = this.nodes.length;
    document.getElementById('connectionCount').textContent = this.connections.length;
    document.getElementById('outcomeCount').textContent =
      this.nodes.filter(n => n.type === 'outcome').length;
    document.getElementById('skillEnhancedCount').textContent =
      this.nodes.filter(n =>
        (n.skillEffects && n.skillEffects.length > 0) ||
        (n.nodeSkillRequirements && n.nodeSkillRequirements.length > 0)
      ).length;

    // Average path length
    const startNodes = this.nodes.filter(n => n.type === 'start');
    const outcomeNodes = this.nodes.filter(n => n.type === 'outcome');
    if (startNodes.length > 0 && outcomeNodes.length > 0) {
      let totalDays = 0;
      let pathCount = 0;
      outcomeNodes.forEach(outcome => {
        const pathEst = this.calculatePathEstimation(outcome.id);
        if (pathEst.min < Infinity && pathEst.max < Infinity) {
          totalDays += (pathEst.min + pathEst.max) / 2;
          pathCount++;
        }
      });
      const avgPath = pathCount > 0 ? Math.round(totalDays / pathCount) : 0;
      document.getElementById('avgPathLength').textContent = `${avgPath} days`;
    } else {
      document.getElementById('avgPathLength').textContent = '0 days';
    }

    // Skill usage stats
    const skillUsage = this.analyzeSkillUsage();
    const skillUsageList = document.getElementById('skillUsageList');
    if (skillUsage.length === 0) {
      skillUsageList.innerHTML =
        '<div style="color: #888; text-align: center; padding: 10px;">No skills used yet</div>';
    } else {
      const maxUsage = Math.max(...skillUsage.map(s => s.score));
      skillUsageList.innerHTML = skillUsage.slice(0, 5).map(stat => `
        <div class="skill-stat-item">
          <div>
            <div style="font-weight: bold; font-size: 12px;">${stat.skill}</div>
            <div style="font-size: 10px; color: #aaa;">
              Used ${stat.usage} times (weight: ${stat.weight.toFixed(1)})
            </div>
            <div class="skill-stat-bar" style="width: ${(stat.score / maxUsage) * 100}%;"></div>
          </div>
        </div>
      `).join('');
    }

    // Outcome analysis
    const outcomes = this.analyzeOutcomeProbabilities();
    const outcomeAnalysisList = document.getElementById('outcomeAnalysisList');
    if (outcomes.length === 0) {
      outcomeAnalysisList.innerHTML =
        '<div style="color: #888; text-align: center; padding: 10px;">No outcomes defined yet</div>';
    } else {
      outcomeAnalysisList.innerHTML = outcomes.map(outcome => `
        <div
          class="outcome-item"
          style="cursor: pointer;"
          onclick="storyBuilder.snapToNode(${outcome.node.id})"
        >
          <div>
            <div style="font-weight: bold; font-size: 12px;">
              ${outcome.node.title}
            </div>
            <div style="font-size: 10px; color: #aaa;">
              ${outcome.node.description || 'No description'}
            </div>
          </div>
          <div class="outcome-probability">
            ${outcome.probability.toFixed(1)}%
          </div>
        </div>
      `).join('');
    }
  }

  snapToNode(nodeId) {
    const node = this.nodes.find(n => n.id === nodeId);
    if (!node) return;
    const container = document.getElementById('canvasContainer');
    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;

    this.offsetX = containerWidth / 2 - node.x * this.scale;
    this.offsetY = containerHeight / 2 - node.y * this.scale;

    this.selectedNodes = [node];
    this.updatePropertiesPanel();
    this.render();
  }

  updatePropertiesPanel() {
    const panel = document.getElementById('nodeProperties');

    // Check if a group is selected
    if (this.selectedGroup && this.selectedNodes.length === 0) {
      this.showGroupProperties();
      return;
    }

    // No nodes selected
    if (this.selectedNodes.length === 0) {
      panel.innerHTML = `
        <div style="text-align: center; margin-top: 50px; color: #888;">
          <p>🎯 Select a node or group to edit properties</p>
          <p style="font-size: 12px; margin-top: 10px;">
            Ctrl+Click to connect nodes<br>
            Right-click to delete<br>
            Shift+Right-click to create groups<br>
            Click group title to edit properties<br>
            Drag to pan the map<br>
            Shift+Click to multi-select
          </p>
        </div>`;
      return;
    }

    // Multiple nodes selected
    if (this.selectedNodes.length > 1) {
      panel.innerHTML = `
        <div style="text-align: center; margin-top: 50px; color: #fff;">
          <p>🎯 ${this.selectedNodes.length} nodes selected</p>
          <p style="font-size: 12px; margin-top: 10px;">
            You can only edit one node at a time.
          </p>
        </div>`;
      return;
    }

    // Exactly one node selected
    const node = this.selectedNodes[0];
    const connections = this.connections.filter(c => c.from === node.id);
    const pathEst = this.calculatePathEstimation(node.id);

    panel.innerHTML = `
      <div class="form-section">
        <h4>Basic Properties</h4>
        <div class="form-group">
          <label>Title:</label>
          <input type="text" value="${node.title}" onchange="storyBuilder.updateNodeProperty('title', this.value)">
        </div>
        <div class="form-group">
          <label>Description:</label>
          <textarea rows="3" onchange="storyBuilder.updateNodeProperty('description', this.value)" placeholder="Describe what happens in this event...">${node.description}</textarea>
        </div>
        <div class="form-group">
          <label>Base Probability: <span id="probValue">${node.probability}%</span></label>
          <input type="range" class="probability-slider" min="0" max="100" value="${node.probability}"
                 oninput="document.getElementById('probValue').textContent = this.value + '%'"
                 onchange="storyBuilder.updateNodeProperty('probability', parseInt(this.value))">
        </div>
        <div class="form-group">
          <label>Node Type:</label>
          <select onchange="storyBuilder.updateNodeProperty('type', this.value)">
            ${Object.keys(this.nodeTypes).map(type =>
              `<option value="${type}" ${node.type === type ? 'selected' : ''}>${this.nodeTypes[type].label}</option>`
            ).join('')}
          </select>
        </div>
      </div>

      <div class="faction-selector">
        <h4>🏴‍☠️ Faction & Traits</h4>
        <div class="form-group">
          <label>Faction:</label>
          <select onchange="storyBuilder.updateNodeProperty('faction', this.value)">
            <option value="">No faction</option>
            <!-- Populate faction options here... -->
          </select>
        </div>
        ${node.faction ? `
          <div style="background: rgba(156, 39, 176, 0.2); padding: 6px; border-radius: 4px; font-size: 11px;">
            <strong>${node.faction}</strong>
          </div>
        ` : ''}
        ${node.traits && node.traits.length > 0 ? `
          <div style="background: rgba(255, 152, 0, 0.2); padding: 6px; border-radius: 4px; margin-top: 6px; font-size: 11px;">
            Traits: <strong>${node.traits.join(', ')}</strong>
          </div>
        ` : ''}
      </div>

      <div class="form-section">
        <h4>Skills & Requirements</h4>
        <div class="input-group">
          <button class="btn btn-primary btn-small" onclick="storyBuilder.openSkillModal(${node.id})">
            🎯 Edit Skills
          </button>
          <button class="btn btn-secondary btn-small" onclick="storyBuilder.openRequirementsBuilder(${node.id})">
            📋 Requirements
          </button>
          <button class="btn btn-secondary btn-small" onclick="storyBuilder.openMetadataBuilder(${node.id})">
            🏷️ Metadata
          </button>
        </div>
        ${node.nodeSkillRequirements && node.nodeSkillRequirements.length > 0 ? `
          <div style="margin-top: 10px; padding: 8px; background: rgba(76, 175, 80, 0.2); border-radius: 4px; font-size: 11px;">
            <strong>Active Skills:</strong> ${node.nodeSkillRequirements.map(r => r.skill).join(', ')}
          </div>
        ` : ''}
      </div>

      ${node.type !== 'start' && (pathEst.min < Infinity && pathEst.max < Infinity) ? `
        <div class="path-estimation">
          <h5>📈 Days to Reach This Node</h5>
          <div style="font-size: 12px;">
            Minimum: <strong>${pathEst.min} days</strong><br>
            Maximum: <strong>${pathEst.max} days</strong>
          </div>
        </div>
      ` : ''}

      <div class="form-section">
        <h4>Outgoing Connections (${connections.length})</h4>
        <div class="connections-list">
          ${connections.map((conn, index) => {
            const toNode = this.nodes.find(n => n.id === conn.to);
            const isReadOnly = connections.length === 1;
            return `
              <div class="connection-item" data-connection-from="${conn.from}" data-connection-to="${conn.to}">
                <div class="connection-header">
                  <span>→ ${toNode ? toNode.title : 'Unknown'}</span>
                  <span class="connection-label" style="background-color: ${this.getConnectionLabelColor(conn.label)};">
                    ${conn.label === 'Custom' ? conn.customLabel : conn.label}
                  </span>
                  <button class="btn btn-danger btn-small" onclick="storyBuilder.deleteConnection(${conn.from}, ${conn.to})">×</button>
                </div>
                <div class="connection-controls">
                  <div class="connection-field">
                    <label>Probability</label>
                    <input type="number" min="0" max="100" value="${conn.probability}" ${isReadOnly ? 'readonly' : ''}
                           onchange="storyBuilder.updateConnectionProbability(${conn.from}, ${conn.to}, parseInt(this.value))">
                  </div>
                  <div class="connection-field">
                    <label>Label</label>
                    <select onchange="storyBuilder.updateConnectionProperty(${conn.from}, ${conn.to}, 'label', this.value)">
                      ${this.CONNECTION_LABELS.map(label =>
                        `<option value="${label}" ${conn.label === label ? 'selected' : ''}>${label}</option>`
                      ).join('')}
                    </select>
                  </div>
                </div>
                <div class="connection-controls">
                  <div class="connection-field">
                    <label>Days Type</label>
                    <select onchange="storyBuilder.updateConnectionDaysType(${conn.from}, ${conn.to}, this.value)">
                      <option value="fixed" ${conn.daysToNext.type === 'fixed' ? 'selected' : ''}>Fixed</option>
                      <option value="dice" ${conn.daysToNext.type === 'dice' ? 'selected' : ''}>Dice Roll</option>
                    </select>
                  </div>
                  <div class="connection-field">
                    <label>${conn.daysToNext.type === 'fixed' ? 'Days' : 'Dice'}</label>
                    <input type="${conn.daysToNext.type === 'fixed' ? 'number' : 'text'}"
                           value="${conn.daysToNext.type === 'fixed' ? conn.daysToNext.value : conn.daysToNext.dice}"
                           onchange="storyBuilder.updateConnectionDaysValue(${conn.from}, ${conn.to}, this.value)"
                           placeholder="${conn.daysToNext.type === 'fixed' ? '1' : '1d3+1'}">
                  </div>
                </div>
                <div class="connection-controls">
                  <div class="connection-field">
                    <label>Faction</label>
                    <select onchange="storyBuilder.updateConnectionProperty(${conn.from}, ${conn.to}, 'faction', this.value)">
                      <option value="">No faction</option>
                      <!-- Populate faction options here… -->
                    </select>
                  </div>
                  <div class="connection-field">
                    <label>Relationship Modifier</label>
                    <select onchange="storyBuilder.updateConnectionRelationshipModifier(${conn.from}, ${conn.to}, this.value)">
                      <option value="0">No modifier</option>
                      <option value="10" ${conn.relationshipModifier === 10 ? 'selected' : ''}>+10% (Allied)</option>
                      <option value="5" ${conn.relationshipModifier === 5 ? 'selected' : ''}>+5% (Friendly)</option>
                      <option value="-5" ${conn.relationshipModifier === -5 ? 'selected' : ''}'>-5% (Neutral)</option>
                      <option value="-10" ${conn.relationshipModifier === -10 ? 'selected' : ''}'>-10% (Hostile)</option>
                      <option value="-20" ${conn.relationshipModifier === -20 ? 'selected' : ''}'>-20% (Enemy)</option>
                    </select>
                  </div>
                </div>
                ${conn.label === 'Custom' ? `
                  <div class="connection-field" style="margin-top: 8px;">
                    <label>Custom Label</label>
                    <input type="text" value="${conn.customLabel}" placeholder="Enter custom label"
                           onchange="storyBuilder.updateConnectionProperty(${conn.from}, ${conn.to}, 'customLabel', this.value)">
                  </div>
                ` : ''}
              </div>
            `;
          }).join('')}
          ${connections.length === 0 ? '<p style="color: #888; font-size: 12px; text-align: center; padding: 20px;">No outgoing connections</p>' : ''}
          ${connections.length > 1 ? '<div style="background: rgba(255, 193, 7, 0.2); padding: 8px; border-radius: 4px; margin-top: 10px; font-size: 11px;">💡 Probabilities auto-redistribute when modified</div>' : ''}
        </div>
      </div>
    `;
  }

  // ─────────────────────────────────────────────────────────────────
  // SKILL REQUIREMENTS MODAL
  // ─────────────────────────────────────────────────────────────────

  openSkillModal(nodeId) {
    this.currentEditingNode = nodeId;
    const node = this.nodes.find(n => n.id === nodeId);
    if (!node) return;
    const modal = document.getElementById('skillModal');
    const content = document.getElementById('skillModalContent');
    content.innerHTML = this.generateSkillModalContent(node);
    modal.style.display = 'block';
  }

  generateSkillModalContent(node) {
    const availableSkills = Object.keys(RIMWORLD_SKILLS).filter(skill =>
      !node.nodeSkillRequirements.some(req => req.skill === skill)
    );

    return `
      <div class="form-section">
        <h4>Node Trigger Skills</h4>
        <p style="color: #ccc; font-size: 12px; margin-bottom: 15px;">
          These skills affect whether this event can trigger at all.
        </p>
        <div class="info-box">
          <span class="icon">!</span>
          <strong>Weight Explanation:</strong> Each skill level below the required maximum reduces this skill's effect chance proportionally. Example: If the max is 10, weight is 1, and the effect percent is 100, each point below 10 reduces the effect by 10%. At skill 8, this would mean only 80% effect is applied.
        </div>
        <div class="input-group" style="margin-bottom: 15px;">
          <select id="newNodeSkill">
            <option value="">Add skill requirement...</option>
            ${availableSkills.map(skill =>
              `<option value="${skill}">${skill} - ${RIMWORLD_SKILLS[skill].description}</option>`
            ).join('')}
          </select>
          <button class="btn btn-primary btn-small" onclick="storyBuilder.addNodeSkillRequirement()">Add</button>
        </div>
        ${node.nodeSkillRequirements.map((req, index) => `
          <div class="skill-item">
            <div class="skill-header">
              <span class="skill-name">${req.skill}</span>
              <button class="btn btn-danger btn-small" onclick="storyBuilder.removeNodeSkillRequirement(${index})">Remove</button>
            </div>
            <div class="skill-controls">
              <div class="skill-input-group">
                <label>Min Level</label>
                <input type="number" min="0" max="20" value="${req.minLevel}"
                       onchange="storyBuilder.updateNodeSkillRequirement(${index}, 'minLevel', this.value)">
                <div class="competency-hint">${SKILL_COMPETENCY[req.minLevel] || 'Unknown'}</div>
              </div>
              <div class="skill-input-group">
                <label>Effect %</label>
                <input type="number" min="0" max="100" value="${req.effectPercent}"
                       onchange="storyBuilder.updateNodeSkillRequirement(${index}, 'effectPercent', this.value)">
              </div>
              <div class="skill-input-group">
                <label>Weight</label>
                <input type="number" min="0" max="5" step="0.1" value="${req.weight}"
                       onchange="storyBuilder.updateNodeSkillRequirement(${index}, 'weight', this.value)">
              </div>
            </div>
          </div>
        `).join('')}
      </div>
    `;
  }

  addNodeSkillRequirement() {
    const select = document.getElementById('newNodeSkill');
    if (!select.value || !this.currentEditingNode) return;
    const node = this.nodes.find(n => n.id === this.currentEditingNode);
    if (!node) return;
    const requirement = {
      skill: select.value,
      minLevel: 10,
      effectPercent: 25,
      weight: 1.0
    };
    node.nodeSkillRequirements.push(requirement);
    const content = document.getElementById('skillModalContent');
    content.innerHTML = this.generateSkillModalContent(node);
    this.markUnsaved();
  }

  removeNodeSkillRequirement(index) {
    const node = this.nodes.find(n => n.id === this.currentEditingNode);
    if (!node) return;
    node.nodeSkillRequirements.splice(index, 1);
    const content = document.getElementById('skillModalContent');
    content.innerHTML = this.generateSkillModalContent(node);
    this.markUnsaved();
  }

  updateNodeSkillRequirement(index, property, value) {
    const node = this.nodes.find(n => n.id === this.currentEditingNode);
    if (!node || !node.nodeSkillRequirements[index]) return;
    if (property === 'minLevel' || property === 'effectPercent') {
      node.nodeSkillRequirements[index][property] = parseInt(value);
    } else if (property === 'weight') {
      node.nodeSkillRequirements[index][property] = parseFloat(value);
    } else {
      node.nodeSkillRequirements[index][property] = value;
    }
    if (property === 'minLevel') {
      // Update competency hint
      // (Competency hint update logic goes here if desired)
    }
    this.markUnsaved();
    this.updateStats();
  }

  closeSkillModal() {
    document.getElementById('skillModal').style.display = 'none';
    this.currentEditingNode = null;
    this.updatePropertiesPanel();
  }

  saveSkillRequirements() {
    this.markUnsaved();
    this.closeSkillModal();
    this.render();
  }

  // ─────────────────────────────────────────────────────────────────
  // REQUIREMENTS BUILDER MODAL
  // ─────────────────────────────────────────────────────────────────

  openRequirementsBuilder(nodeId) {
    this.currentEditingNode = nodeId;
    const node = this.nodes.find(n => n.id === nodeId);
    if (!node) return;
    const modal = document.getElementById('requirementsModal');
    const content = document.getElementById('requirementsModalContent');
    content.innerHTML = this.generateRequirementsBuilderContent(node);
    modal.style.display = 'block';
  }

  generateRequirementsBuilderContent(node) {
    return `
      <div class="form-section">
        <h4>Add Requirement</h4>
        <div class="requirements-builder">
          <input type="text" id="newRequirement" placeholder="e.g., trait:tough, faction:pirates, health>50">
          <button class="btn btn-primary btn-small" onclick="storyBuilder.addRequirement()">Add</button>
        </div>
        <h4>Current Requirements</h4>
        <div id="requirementsList">
          ${node.requirements.map((req, index) => `
            <div class="requirement-item">
              <span>${req}</span>
              <button class="btn btn-danger btn-small" onclick="storyBuilder.removeRequirement(${index})">Remove</button>
            </div>
          `).join('')}
          ${node.requirements.length === 0 ? '<p style="color: #888;">No requirements set</p>' : ''}
        </div>
        <h4>JSON View</h4>
        <textarea id="requirementsJson" rows="5" style="width: 100%;">${JSON.stringify(node.requirements, null, 2)}</textarea>
      </div>
    `;
  }

  addRequirement() {
    const input = document.getElementById('newRequirement');
    if (!input.value.trim() || !this.currentEditingNode) return;
    const node = this.nodes.find(n => n.id === this.currentEditingNode);
    if (!node) return;
    node.requirements.push(input.value.trim());
    input.value = '';
    const content = document.getElementById('requirementsModalContent');
    content.innerHTML = this.generateRequirementsBuilderContent(node);
    this.markUnsaved();
  }

  removeRequirement(index) {
    const node = this.nodes.find(n => n.id === this.currentEditingNode);
    if (!node) return;
    node.requirements.splice(index, 1);
    const content = document.getElementById('requirementsModalContent');
    content.innerHTML = this.generateRequirementsBuilderContent(node);
    this.markUnsaved();
  }

  closeRequirementsModal() {
    document.getElementById('requirementsModal').style.display = 'none';
    this.currentEditingNode = null;
    this.updatePropertiesPanel();
  }

  saveRequirements() {
    const textarea = document.getElementById('requirementsJson');
    if (textarea && this.currentEditingNode) {
      try {
        const node = this.nodes.find(n => n.id === this.currentEditingNode);
        if (node) {
          node.requirements = JSON.parse(textarea.value);
        }
      } catch (e) {
        alert('Invalid JSON in requirements');
        return;
      }
    }
    this.closeRequirementsModal();
  }

  // ─────────────────────────────────────────────────────────────────
  // METADATA BUILDER MODAL
  // ─────────────────────────────────────────────────────────────────

  openMetadataBuilder(nodeId) {
    this.currentEditingNode = nodeId;
    const node = this.nodes.find(n => n.id === nodeId);
    if (!node) return;
    const modal = document.getElementById('metadataModal');
    const content = document.getElementById('metadataModalContent');
    content.innerHTML = this.generateMetadataBuilderContent(node);
    modal.style.display = 'block';
  }

  generateMetadataBuilderContent(node) {
    const metadata = node.metadata || {};
    const entries = Object.entries(metadata);

    return `
      <div class="faction-selector">
        <h4>🏴‍☠️ Faction Assignment</h4>
        <div class="form-group">
          <label>Controlling/Involved Faction:</label>
          <select onchange="storyBuilder.updateNodeFaction(${node.id}, this.value)">
            <option value="">No faction assigned</option>
            <!-- Populate faction options here… -->
          </select>
        </div>
        ${node.faction ? `
          <div style="background: rgba(156, 39, 176, 0.2); padding: 8px; border-radius: 4px; margin-top: 8px; font-size: 11px;">
            Current faction: <strong>${node.faction}</strong>
          </div>
        ` : ''}
      </div>

      <div class="trait-selector">
        <h4>🎭 Character Traits</h4>
        <!-- Populate traits here… -->
        ${node.traits && node.traits.length > 0 ? `
          <div style="background: rgba(255, 152, 0, 0.2); padding: 8px; border-radius: 4px; margin-top: 8px; font-size: 11px;">
            Active traits: <strong>${node.traits.join(', ')}</strong>
          </div>
        ` : ''}
      </div>

      <div class="form-section">
        <h4>Add Metadata</h4>
        <div class="metadata-builder">
          <input type="text" id="newMetaKey" placeholder="Key (e.g., flavor, difficulty)">
          <input type="text" id="newMetaValue" placeholder="Value">
          <button class="btn btn-primary btn-small" onclick="storyBuilder.addMetadata()">Add</button>
        </div>
        <h4>Current Metadata</h4>
        <div id="metadataList">
          ${entries.map(([key, value], index) => `
            <div class="requirement-item">
              <span><strong>${key}:</strong> ${value}</span>
              <button class="btn btn-danger btn-small" onclick="storyBuilder.removeMetadata('${key}')">Remove</button>
            </div>
          `).join('')}
          ${entries.length === 0 ? '<p style="color: #888;">No metadata set</p>' : ''}
        </div>
        <h4>JSON View</h4>
        <textarea id="metadataJson" rows="5" style="width: 100%;">${JSON.stringify(metadata, null, 2)}</textarea>
      </div>
    `;
  }

  updateNodeFaction(nodeId, faction) {
    const node = this.nodes.find(n => n.id === parseInt(nodeId));
    if (!node) return;
    node.faction = faction || null;
    this.markUnsaved();
    this.recordHistory();
    const content = document.getElementById('metadataModalContent');
    content.innerHTML = this.generateMetadataBuilderContent(node);
  }

  toggleTrait(nodeId, trait) {
    const node = this.nodes.find(n => n.id === parseInt(nodeId));
    if (!node) return;
    if (!node.traits) node.traits = [];
    const idx = node.traits.indexOf(trait);
    if (idx === -1) node.traits.push(trait);
    else node.traits.splice(idx, 1);
    this.markUnsaved();
    this.recordHistory();
    const content = document.getElementById('metadataModalContent');
    content.innerHTML = this.generateMetadataBuilderContent(node);
  }

  addMetadata() {
    const keyInput = document.getElementById('newMetaKey');
    const valueInput = document.getElementById('newMetaValue');
    if (!keyInput.value.trim() || !valueInput.value.trim() || !this.currentEditingNode) return;
    const node = this.nodes.find(n => n.id === this.currentEditingNode);
    if (!node) return;
    if (!node.metadata) node.metadata = {};
    node.metadata[keyInput.value.trim()] = valueInput.value.trim();
    keyInput.value = '';
    valueInput.value = '';
    const content = document.getElementById('metadataModalContent');
    content.innerHTML = this.generateMetadataBuilderContent(node);
    this.markUnsaved();
  }

  removeMetadata(key) {
    const node = this.nodes.find(n => n.id === this.currentEditingNode);
    if (!node || !node.metadata) return;
    delete node.metadata[key];
    const content = document.getElementById('metadataModalContent');
    content.innerHTML = this.generateMetadataBuilderContent(node);
    this.markUnsaved();
  }

  closeMetadataModal() {
    document.getElementById('metadataModal').style.display = 'none';
    this.currentEditingNode = null;
    this.updatePropertiesPanel();
  }

  saveMetadata() {
    const textarea = document.getElementById('metadataJson');
    if (textarea && this.currentEditingNode) {
      try {
        const node = this.nodes.find(n => n.id === this.currentEditingNode);
        if (node) node.metadata = JSON.parse(textarea.value);
      } catch (e) {
        alert('Invalid JSON in metadata');
        return;
      }
    }
    this.markUnsaved();
    this.recordHistory();
    this.closeMetadataModal();
  }

/**
 * render():
 *   1) Clear entire canvas
 *   2) Enter world-space transform (translate + scale)
 *   3) Draw all connections
 *   4) Draw all nodes
 *   5) Exit world-space back to screen
 *   6) Draw remote cursors (circle + name) in screen-space
 *   7) If box-select is active, overlay a translucent, dashed rectangle
 */
 render() {
  if (!Array.isArray(this.nodes) || !Array.isArray(this.connections)) {
    console.warn('Render called before nodes/connections initialized:', this.nodes, this.connections);
    return;
  }
  // 1) Clear previous frame
  this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

  // Clear connection label areas for click detection
  this.connectionLabelAreas = [];

  // 2) Enter world-space (scale & translate)
  this.ctx.save();
  this.ctx.translate(this.offsetX, this.offsetY);
  this.ctx.scale(this.scale, this.scale);

  // Initialize storyGroups if not already initialized (don't reset existing groups!)
  if (!this.storyGroups) {
    this.storyGroups = [];
  }

  // 3) Draw all connections (your existing drawConnection logic)
  this.connections.forEach(conn => {
    this.drawConnection(conn);
  });

  // 4) Draw all nodes on top of connections (your existing drawNode logic)
  this.nodes.forEach(node => {
    this.drawNode(node);
  });

  // 5) Exit world-space
  this.ctx.restore();

  // 6) Draw remote cursors (in screen-space). Each cursor shows a small circle + the user’s name.
  if (this.remoteCursors) {
    Object.entries(this.remoteCursors).forEach(([sid, cursor]) => {
      const { x: worldX, y: worldY, color, name } = cursor;
      // Convert world coords → screen coords:
      const screenX = worldX * this.scale + this.offsetX;
      const screenY = worldY * this.scale + this.offsetY;

      // 6a) Draw a small filled circle at (screenX, screenY)
      this.ctx.beginPath();
      this.ctx.arc(screenX, screenY, 5, 0, 2 * Math.PI);
      this.ctx.fillStyle = color || '#00ff00';
      this.ctx.fill();
      this.ctx.closePath();

      // 6b) Draw the user’s name just above/right of the circle
      this.ctx.font = '12px sans-serif';
      this.ctx.fillStyle = '#ffffff';
      this.ctx.textBaseline = 'bottom';   // so text sits above the circle
      this.ctx.fillText(name || 'Anonymous', screenX + 8, screenY - 8);
    });
  }

  // 7) Draw YOUR box-selection rectangle if active (in screen-space)
  if (this.isBoxSelecting) {
    const x1 = this.boxStartX;
    const y1 = this.boxStartY;
    const x2 = this.boxCurrentX;
    const y2 = this.boxCurrentY;

    // Normalize to top-left corner + positive width/height
    const left   = Math.min(x1, x2);
    const top    = Math.min(y1, y2);
    const width  = Math.abs(x2 - x1);
    const height = Math.abs(y2 - y1);

    this.ctx.save();
    // 7a) Semi-transparent fill
    this.ctx.globalAlpha = 0.15;
    this.ctx.fillStyle   = '#ffffff';
    this.ctx.fillRect(left, top, width, height);
    // Restore full opacity for outline
    this.ctx.globalAlpha = 1.0;

    // 7b) Dashed outline
    this.ctx.beginPath();
    this.ctx.setLineDash([6, 4]);
    this.ctx.lineWidth   = 2;
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.strokeRect(left, top, width, height);
    this.ctx.setLineDash([]); // reset dash pattern
    this.ctx.restore();
  }

  // 8) Draw remote users' box-selection rectangles (in screen-space)
  if (this.remoteBoxSelections) {
    Object.entries(this.remoteBoxSelections).forEach(([userID, selection]) => {
      // Convert world coordinates back to screen coordinates
      const x1Screen = selection.x1 * this.scale + this.offsetX;
      const y1Screen = selection.y1 * this.scale + this.offsetY;
      const x2Screen = selection.x2 * this.scale + this.offsetX;
      const y2Screen = selection.y2 * this.scale + this.offsetY;

      // Normalize to top-left corner + positive width/height
      const left   = Math.min(x1Screen, x2Screen);
      const top    = Math.min(y1Screen, y2Screen);
      const width  = Math.abs(x2Screen - x1Screen);
      const height = Math.abs(y2Screen - y1Screen);

      this.ctx.save();
      // Semi-transparent fill in user's color
      this.ctx.globalAlpha = 0.1;
      this.ctx.fillStyle = selection.userColor;
      this.ctx.fillRect(left, top, width, height);
      // Restore full opacity for outline
      this.ctx.globalAlpha = 1.0;

      // Dashed outline in user's color
      this.ctx.beginPath();
      this.ctx.setLineDash([4, 4]);
      this.ctx.lineWidth = 2;
      this.ctx.strokeStyle = selection.userColor;
      this.ctx.strokeRect(left, top, width, height);
      this.ctx.setLineDash([]); // reset dash pattern
      this.ctx.restore();
    });
  }
 

  // 9) Draw story group containers
  this.storyGroups.forEach(group => {
    this.drawGroupContainer(group);
  });

  // 10) Draw ghost node if dragging from palette
  if (this.isDraggingNewNode && this.ghostNode) {
    this.drawGhostNode(this.ghostNode);
  }

  // 11) Draw live cursor coordinates
  if (this.showCursorCoordinates && this.currentMouseWorldX !== undefined) {
    this.drawCursorCoordinates();
  }

}



  drawNode(node) {
    const isSelected = this.selectedNodes.some(n => n.id === node.id);
    const nodeType = this.nodeTypes[node.type];

    // Check if this node is selected by any remote user
    let remoteUserColor = null;
    for (const userId in this.remoteSelections) {
      if (this.remoteSelections[userId].nodeId === node.id) {
        remoteUserColor = this.remoteSelections[userId].userColor;
        break;
      }
    }

    // Shadow
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.4)';
    this.ctx.beginPath();
    this.ctx.arc(node.x + 3, node.y + 3, 28, 0, Math.PI * 2);
    this.ctx.fill();

    // Gradient fill
    const gradient = this.ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, 25);
    gradient.addColorStop(0, nodeType.color);
    gradient.addColorStop(1, this.darkenColor(nodeType.color, 0.3));

    this.ctx.fillStyle = gradient;

    // Determine stroke color and width based on selection state
    let strokeColor = '#333';
    let lineWidth = 2;

    if (isSelected) {
      // Local user selection - white border
      strokeColor = '#fff';
      lineWidth = 4;
    } else if (remoteUserColor) {
      // Remote user selection - use their color
      strokeColor = remoteUserColor;
      lineWidth = 3;
    }

    this.ctx.strokeStyle = strokeColor;
    this.ctx.lineWidth = lineWidth;
    this.ctx.beginPath();
    this.ctx.arc(node.x, node.y, 25, 0, Math.PI * 2);
    this.ctx.fill();
    this.ctx.stroke();

    // Draw additional outer ring for remote selections to make them more visible
    if (remoteUserColor && !isSelected) {
      this.ctx.strokeStyle = remoteUserColor;
      this.ctx.lineWidth = 2;
      this.ctx.setLineDash([5, 5]); // Dashed line
      this.ctx.beginPath();
      this.ctx.arc(node.x, node.y, 30, 0, Math.PI * 2);
      this.ctx.stroke();
      this.ctx.setLineDash([]); // Reset line dash
    }

    // Indicators: skill, trait, faction
    let indicatorOffset = 0;
    const hasSkill = node.nodeSkillRequirements && node.nodeSkillRequirements.length > 0;
    const hasTraits = node.traits && node.traits.length > 0;
    const hasFaction = !!node.faction;

    if (hasSkill) {
      this.ctx.fillStyle = '#4CAF50';
      this.ctx.beginPath();
      this.ctx.arc(node.x + 20, node.y - 20 + indicatorOffset, 8, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.fillStyle = '#fff';
      this.ctx.font = 'bold 10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('S', node.x + 20, node.y - 16 + indicatorOffset);
      indicatorOffset += 18;
    }
    if (hasTraits) {
      this.ctx.fillStyle = '#FF9800';
      this.ctx.beginPath();
      this.ctx.arc(node.x + 20, node.y - 20 + indicatorOffset, 8, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.fillStyle = '#fff';
      this.ctx.font = 'bold 10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('T', node.x + 20, node.y - 16 + indicatorOffset);
      indicatorOffset += 18;
    }
    if (hasFaction) {
      this.ctx.fillStyle = '#9C27B0';
      this.ctx.beginPath();
      this.ctx.arc(node.x + 20, node.y - 20 + indicatorOffset, 8, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.fillStyle = '#fff';
      this.ctx.font = 'bold 10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('F', node.x + 20, node.y - 16 + indicatorOffset);
    }

    // Node type label
    this.ctx.fillStyle = '#fff';
    this.ctx.font = 'bold 11px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(nodeType.label.substr(0, 4), node.x, node.y + 4);

    // Probability
    this.ctx.font = '9px Arial';
    this.ctx.fillStyle = '#FFE082';
    this.ctx.fillText(`${node.probability}%`, node.x, node.y + 42);

    // Title (full title, no truncation)
    this.ctx.font = 'bold 13px Arial';
    this.ctx.fillStyle = '#fff';
    this.ctx.fillText(node.title, node.x, node.y - 38);

    // Faction name (if any)
    if (hasFaction) {
      this.ctx.font = '8px Arial';
      this.ctx.fillStyle = '#E1BEE7';
      this.ctx.fillText(node.faction, node.x, node.y - 50);
    }

    // Path estimation if selected and not a start node
    if (isSelected && node.type !== 'start') {
      const pathEst = this.calculatePathEstimation(node.id);
      if (pathEst.min < Infinity && pathEst.max < Infinity) {
        this.ctx.font = '8px Arial';
        this.ctx.fillStyle = '#FFC107';
        this.ctx.fillText(`${pathEst.min}-${pathEst.max} days`, node.x, node.y + 55);
      }
    }
  }

  drawConnection(conn) {
    const fromNode = this.nodes.find(n => n.id === conn.from);
    const toNode = this.nodes.find(n => n.id === conn.to);
    if (!fromNode || !toNode) return;

    const labelColor = this.getConnectionLabelColor(conn.label);
    this.ctx.strokeStyle = labelColor;
    this.ctx.lineWidth = 3;

    // Handle self-loop connections (node connected to itself)
    if (conn.from === conn.to) {
      // Draw a curved loop on the top-right of the node
      const loopRadius = 20;
      const centerX = fromNode.x + 25;
      const centerY = fromNode.y - 25;

      this.ctx.beginPath();
      this.ctx.arc(centerX, centerY, loopRadius, 0, Math.PI * 1.5);
      this.ctx.stroke();

      // Draw arrowhead at the end of the loop
      const arrowLength = 12;
      const arrowX = centerX - loopRadius * 0.7;
      const arrowY = centerY + loopRadius * 0.7;
      const angle = Math.PI * 0.25; // 45 degrees

      this.ctx.fillStyle = labelColor;
      this.ctx.beginPath();
      this.ctx.moveTo(arrowX, arrowY);
      this.ctx.lineTo(arrowX - arrowLength * Math.cos(angle - Math.PI / 6),
                      arrowY - arrowLength * Math.sin(angle - Math.PI / 6));
      this.ctx.lineTo(arrowX - arrowLength * Math.cos(angle + Math.PI / 6),
                      arrowY - arrowLength * Math.sin(angle + Math.PI / 6));
      this.ctx.closePath();
      this.ctx.fill();

      // Position labels for self-loop
      const midX = centerX + loopRadius + 10;
      const midY = centerY;

      // Draw labels
      this.drawConnectionLabels(conn, midX, midY, labelColor);
      return;
    }

    // Regular connection between different nodes
    const dx = toNode.x - fromNode.x;
    const dy = toNode.y - fromNode.y;
    const length = Math.sqrt(dx * dx + dy * dy);
    const unitX = dx / length;
    const unitY = dy / length;

    const startX = fromNode.x + unitX * 25;
    const startY = fromNode.y + unitY * 25;
    const endX = toNode.x - unitX * 25;
    const endY = toNode.y - unitY * 25;

    this.ctx.beginPath();
    this.ctx.moveTo(startX, startY);
    this.ctx.lineTo(endX, endY);
    this.ctx.stroke();

    // Arrowhead
    const arrowLength = 12;
    const angle = Math.atan2(dy, dx);
    const arrowX1 = endX - arrowLength * Math.cos(angle - Math.PI / 6);
    const arrowY1 = endY - arrowLength * Math.sin(angle - Math.PI / 6);
    const arrowX2 = endX - arrowLength * Math.cos(angle + Math.PI / 6);
    const arrowY2 = endY - arrowLength * Math.sin(angle + Math.PI / 6);

    this.ctx.fillStyle = labelColor;
    this.ctx.beginPath();
    this.ctx.moveTo(endX, endY);
    this.ctx.lineTo(arrowX1, arrowY1);
    this.ctx.lineTo(arrowX2, arrowY2);
    this.ctx.closePath();
    this.ctx.fill();

    // Midpoint for labels
    const midX = (startX + endX) / 2;
    const midY = (startY + endY) / 2;

    // Draw labels
    this.drawConnectionLabels(conn, midX, midY, labelColor);
  }

  drawConnectionLabels(conn, midX, midY, labelColor) {
    // Probability
    this.ctx.fillStyle = '#ffeb3b';
    this.ctx.font = 'bold 11px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
    this.ctx.lineWidth = 3;
    this.ctx.strokeText(`${conn.probability}%`, midX, midY - 8);
    this.ctx.fillText(`${conn.probability}%`, midX, midY - 8);

    // Label with clickable area
    this.ctx.fillStyle = labelColor;
    this.ctx.font = 'bold 9px Arial';
    const displayLabel = conn.label === 'Custom' ? conn.customLabel : conn.label;

    // Store label bounds for click detection (convert to screen coordinates)
    const screenMidX = midX * this.scale + this.offsetX;
    const screenMidY = midY * this.scale + this.offsetY;
    const labelMetrics = this.ctx.measureText(displayLabel);
    const labelWidth = labelMetrics.width * this.scale;
    const labelHeight = 12 * this.scale; // Approximate text height

    // Store clickable area for this connection label
    if (!this.connectionLabelAreas) this.connectionLabelAreas = [];
    this.connectionLabelAreas.push({
      connection: conn,
      x: screenMidX - labelWidth/2,
      y: screenMidY + 8 * this.scale - labelHeight/2,
      width: labelWidth,
      height: labelHeight
    });

    this.ctx.strokeText(displayLabel, midX, midY + 8);
    this.ctx.fillText(displayLabel, midX, midY + 8);

    // Days delay
    const daysText = this.formatDaysDelay(conn.daysToNext);
    this.ctx.fillStyle = '#81C784';
    this.ctx.font = '8px Arial';
    this.ctx.strokeText(daysText, midX, midY + 20);
    this.ctx.fillText(daysText, midX, midY + 20);

    // Faction on connection
    if (conn.faction) {
      this.ctx.fillStyle = '#E1BEE7';
      this.ctx.font = '7px Arial';
      this.ctx.strokeText(conn.faction, midX, midY + 32);
      this.ctx.fillText(conn.faction, midX, midY + 32);
    }
  }

  getConnectionLabelColor(label) {
    switch (label) {
      case 'Connection': return '#4CAF50';
      case 'Success': return '#00E676';
      case 'Failure': return '#f44336';
      case 'Partial Success': return '#FF9800';
      case 'Critical Failure': return '#9C27B0';
      default: return '#2196F3';
    }
  }

  // Draw story group container
  drawGroupContainer(group) {
    console.warn('Render works to here drawGroupContainer!');
    const x = group.x * this.scale + this.offsetX;
    const y = group.y * this.scale + this.offsetY;
    const width = group.width * this.scale;
    const height = group.height * this.scale;

    this.ctx.save();
console.warn('Render works to here At save!');
    // Draw semi-transparent background
    this.ctx.globalAlpha = 0.2;
    this.ctx.fillStyle = group.color;
    this.ctx.fillRect(x, y, width, height);
console.warn('Render works to here Draw semi-transparent background!');
    // Draw border
    this.ctx.globalAlpha = 0.8;
    this.ctx.strokeStyle = group.color;
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([5, 5]);
    this.ctx.strokeRect(x, y, width, height);
    this.ctx.setLineDash([]);
console.warn('Render works to here Draw border!');
    // Draw title background
    this.ctx.globalAlpha = 0.9;
    this.ctx.fillStyle = group.color;
    this.ctx.fillRect(x, y, width, 25 * this.scale);
console.warn('Render works to here title background!');
    // Draw title text
    this.ctx.globalAlpha = 1.0;
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = `${12 * this.scale}px Arial`;
    this.ctx.textAlign = 'left';
    this.ctx.fillText(group.name, x + 5, y + 16 * this.scale);

    // Draw resize handles if group is selected
    if (this.selectedGroup && this.selectedGroup.id === group.id) {
      this.drawGroupHandles(group);
    }

    this.ctx.restore();
  }

  // Draw resize handles for group
  drawGroupHandles(group) {
    if (!group.dragHandles) return;

    this.ctx.fillStyle = '#ffffff';
    this.ctx.strokeStyle = '#000000';
    this.ctx.lineWidth = 1;

    Object.values(group.dragHandles).forEach(handle => {
      const x = handle.x * this.scale + this.offsetX;
      const y = handle.y * this.scale + this.offsetY;
      const size = handle.width * this.scale;

      this.ctx.fillRect(x, y, size, size);
      this.ctx.strokeRect(x, y, size, size);
    });
  }

  // Draw ghost node during drag from palette
  drawGhostNode(ghostNode) {
    const x = ghostNode.x * this.scale + this.offsetX;
    const y = ghostNode.y * this.scale + this.offsetY;
    const radius = 25 * this.scale;

    this.ctx.save();
    this.ctx.globalAlpha = 0.5;

    // Draw ghost node circle
    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fillStyle = this.nodeTypes[ghostNode.type].color;
    this.ctx.fill();
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();

    // Draw ghost node label
    this.ctx.globalAlpha = 0.8;
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = `${10 * this.scale}px Arial`;
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.nodeTypes[ghostNode.type].label, x, y + 4 * this.scale);

    this.ctx.restore();
  }

  // Draw live cursor coordinates
  drawCursorCoordinates() {
    if (this.currentMouseWorldX === undefined || this.currentMouseWorldY === undefined) return;

    const x = Math.round(this.currentMouseWorldX);
    const y = Math.round(this.currentMouseWorldY);
    const text = `X: ${x}, Y: ${y}`;

    this.ctx.save();

    // Measure text to get proper background size
    this.ctx.font = '14px Arial';
    const textMetrics = this.ctx.measureText(text);
    const textWidth = textMetrics.width;
    const padding = 8;

    // Draw background
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
    this.ctx.fillRect(10, 10, textWidth + padding * 2, 28);

    // Draw border
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.lineWidth = 1;
    this.ctx.strokeRect(10, 10, textWidth + padding * 2, 28);

    // Draw text
    this.ctx.fillStyle = '#ffffff';
    this.ctx.textAlign = 'left';
    this.ctx.textBaseline = 'middle';
    this.ctx.fillText(text, 10 + padding, 24);

    this.ctx.restore();
  }

  formatDaysDelay(daysToNext) {
    if (daysToNext.type === 'fixed') {
      return `${daysToNext.value}d`;
    } else if (daysToNext.type === 'dice') {
      return daysToNext.dice;
    }
    return '1d';
  }

  darkenColor(color, factor) {
    const hex = color.replace('#', '');
    const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));
    const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));
    const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));
    return `rgb(${r}, ${g}, ${b})`;
  }

  // ─────────────────────────────────────────────────────────────────
  // STORY LIST & SAVING/LOADING
  // ─────────────────────────────────────────────────────────────────

  async refreshStoryList() {
    try {
      const stories = await this.database.getAllStories();
      const listElement = document.getElementById('storyList');
      if (stories.length === 0) {
        listElement.innerHTML = '<div style="color: #888; text-align: center; padding: 20px;">No saved stories found</div>';
        return;
      }
      listElement.innerHTML = stories.map(story => `
        <div class="story-item" onclick="storyBuilder.loadSavedStory('${story.name}')">
          <div style="font-weight: bold;">${story.name}</div>
          <div style="font-size: 10px; color: #aaa;">
            ${story.data.nodes?.length || 0} nodes, ${story.data.connections?.length || 0} connections
            <br>Saved: ${new Date(story.modified || story.created).toLocaleString()}
          </div>
        </div>
      `).join('');
    } catch (error) {
      console.error('Failed to refresh story list:', error);
      document.getElementById('storyList').innerHTML = '<div style="color: #f44336; text-align: center; padding: 20px;">Error loading stories</div>';
    }
  }

  async loadSavedStory(name) {
    try {
      const storyRecord = await this.database.loadStory(name);
      if (!storyRecord) return;
      const data = storyRecord.data;
      this.nodes = data.nodes || [];
      this.connections = data.connections || [];
      this.storyGroups = data.groups || [];

      // Auto-attach nodes to groups based on position
      this.autoAttachNodesToGroups();

      // Ensure required properties exist
      this.nodes.forEach(node => {
        if (!node.nodeSkillRequirements) node.nodeSkillRequirements = [];
        if (!node.connectionSkillRequirements) node.connectionSkillRequirements = {};
        if (!node.skillEffects) node.skillEffects = [];
        if (!node.requirements) node.requirements = [];
        if (!node.metadata) node.metadata = {};
        if (!node.traits) node.traits = [];
        if (!node.factionRelationshipModifiers) node.factionRelationshipModifiers = {};
      });
      this.connections.forEach(conn => {
        if (!conn.label) conn.label = 'Connection';
        if (!conn.customLabel) conn.customLabel = '';
        if (!conn.daysToNext) conn.daysToNext = { type: 'fixed', value: 1 };
        if (!conn.traits) conn.traits = [];
        if (!conn.factionProbabilityModifiers) conn.factionProbabilityModifiers = {};
        if (conn.relationshipModifier === undefined) conn.relationshipModifier = 0;
      });

      this.selectedNodes = [];
      this.draggedNode = null;
      this.nextNodeId = this.nodes.reduce((max, n) => Math.max(max, n.id), 0) + 1;

      // Fix any duplicate node IDs that might exist
      this.fixDuplicateNodeIds();

      this.currentStoryName = name;
      this.markSaved();
      this.updatePropertiesPanel();
      this.updateStats();
      this.render();

      if (window.innerWidth <= 768) {
        // close mobile panels if needed
        closeMobilePanels();
      }
    } catch (error) {
      console.error('Failed to load story:', error);
      alert('Failed to load story: ' + error.message);
    }
  }

  async saveStory() {
    let title = this.currentStoryName || prompt("Enter a name for this story:", "My Story");
    if (!title) return;

    try {
      const storyData = {
        nodes: this.nodes,
        connections: this.connections,
        groups: this.storyGroups || [],
        metadata: {
          created: Date.now(),
          version: "1.4"
        }
      };
      await this.database.saveStory(title, storyData);
      this.currentStoryName = title;
      this.markSaved();
      this.refreshStoryList();

      const indicator = document.getElementById('autosaveIndicator');
      const text = document.getElementById('autosaveText');
      text.textContent = '💾 Saved!';
      indicator.classList.add('success-flash');
      setTimeout(() => {
        indicator.classList.remove('success-flash');
        text.textContent = '💾 Autosaved';
      }, 2000);

      const exportJSON = confirm("Export to file as well?");
      if (exportJSON) {
        const blob = new Blob([JSON.stringify(storyData, null, 2)], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = title.replace(/[^\w\-]/g, "_") + ".json";
        document.body.appendChild(a);
        a.click();
        setTimeout(() => {
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }, 500);
      }
    } catch (error) {
      console.error('Failed to save story:', error);
      alert('Failed to save story: ' + error.message);
    }
  }

  

  loadStory() {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result);
          this.nodes = data.nodes || [];
          this.connections = data.connections || [];
          this.storyGroups = data.groups || [];

          // Auto-attach nodes to groups based on position
          this.autoAttachNodesToGroups();

          // Ensure required properties
          this.nodes.forEach(node => {
            if (!node.nodeSkillRequirements) node.nodeSkillRequirements = [];
            if (!node.connectionSkillRequirements) node.connectionSkillRequirements = {};
            if (!node.skillEffects) node.skillEffects = [];
            if (!node.requirements) node.requirements = [];
            if (!node.metadata) node.metadata = {};
            if (!node.traits) node.traits = [];
            if (!node.factionRelationshipModifiers) node.factionRelationshipModifiers = {};
          });
          this.connections.forEach(conn => {
            if (!conn.label) conn.label = 'Connection';
            if (!conn.customLabel) conn.customLabel = '';
            if (!conn.daysToNext) conn.daysToNext = { type: 'fixed', value: 1 };
            if (!conn.traits) conn.traits = [];
            if (!conn.factionProbabilityModifiers) conn.factionProbabilityModifiers = {};
            if (conn.relationshipModifier === undefined) conn.relationshipModifier = 0;
          });

          this.selectedNodes = [];
          this.draggedNode = null;
          this.nextNodeId = this.nodes.reduce((max, n) => Math.max(max, n.id), 0) + 1;

          // Fix any duplicate node IDs that might exist
          this.fixDuplicateNodeIds();

          this.currentStoryName = file.name.replace('.json', '');
          this.markSaved();
          this.updatePropertiesPanel();
          this.updateStats();
          this.render();
        } catch (err) {
          alert("Failed to load file: " + err);
        }
      };
      reader.readAsText(file);
    };
    input.click();
  }

  clear() {
    if (!confirm("Clear ALL nodes and connections?")) return;
    this.nodes = [];
    this.connections = [];
    this.selectedNodes = [];
    this.draggedNode = null;
    this.nextNodeId = 1;
    this.currentStoryName = null;

    // Reset node type counters
    this.nodeTypeCounters = {
      'start': 0,
      'travel': 0,
      'camp': 0,
      'rumor': 0,
      'rescue': 0,
      'outcome': 0
    };

    this.markUnsaved();
    this.recordHistory();
    this.updatePropertiesPanel();
    this.updateStats();
    this.render();
  }

  async openStoryBrowser() {
    const modal = document.getElementById("storyBrowserModal");
    const content = document.getElementById("storyBrowserContent");
    try {
      const stories = await this.database.getAllStories();
      if (stories.length === 0) {
        content.innerHTML = `<div style="color: #888; text-align: center; padding: 20px;">No saved stories found</div>`;
      } else {
        content.innerHTML = `
          <div style="margin-bottom: 20px;">
            <h4>Select a story to load:</h4>
          </div>
          ${stories.map(story => `
            <div class="story-item" onclick="storyBuilder.loadSavedStory('${story.name}'); closeStoryBrowser();" style="margin-bottom: 10px; position: relative;">
              <div style="font-weight: bold; display: flex; justify-content: space-between; align-items: center;">
                <span>${story.name}</span>
                <button class="btn btn-danger btn-small" onclick="event.stopPropagation(); storyBuilder.deleteStoryFromBrowser('${story.name}')">
                  🗑️
                </button>
              </div>
              <div style="font-size: 10px; color: #aaa;">
                ${story.data.nodes?.length || 0} nodes, ${story.data.connections?.length || 0} connections
                <br>Last modified: ${new Date(story.modified || story.created).toLocaleString()}
              </div>
            </div>
          `).join('')}
        `;
      }
    } catch (error) {
      content.innerHTML = `<div style="color: #f44336; text-align: center; padding: 20px;">Error loading stories: ${error.message}</div>`;
    }
    modal.style.display = "block";
  }

  async deleteStoryFromBrowser(name) {
    if (!confirm(`Delete story "${name}"? This cannot be undone.`)) return;
    try {
      await this.database.deleteStory(name);
      this.refreshStoryList();
      this.openStoryBrowser();
    } catch (error) {
      console.error('Failed to delete story:', error);
      alert('Failed to delete story: ' + error.message);
    }
  }
  // Helper to pick a random pastel color for each client’s cursor
_randomCursorColor() {
  const hue = Math.floor(Math.random() * 360);
  return `hsl(${hue}, 70%, 60%)`;
}

  // ─────────────────────────────────────────────────────────────────
  // MULTIPLAYER HELPER FUNCTIONS
  // ─────────────────────────────────────────────────────────────────

  showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      font-weight: bold;
      z-index: 10000;
      opacity: 0;
      transition: opacity 0.3s ease;
    `;

    // Set background color based on type
    switch (type) {
      case 'success': toast.style.backgroundColor = '#4CAF50'; break;
      case 'error': toast.style.backgroundColor = '#f44336'; break;
      case 'warning': toast.style.backgroundColor = '#ff9800'; break;
      default: toast.style.backgroundColor = '#2196F3'; break;
    }

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => toast.style.opacity = '1', 10);

    // Remove after 5 seconds
    setTimeout(() => {
      toast.style.opacity = '0';
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 5000);
  }

  updateLobbyUI() {
    // Update lobby user list if in multiplayer mode
    if (!this.isMultiplayerMode) return;

    const lobbyPanel = document.getElementById('lobbyPanel');
    if (!lobbyPanel) return;

    let html = '<h4>Connected Users</h4>';
    for (const userId in this.lobbyUsers) {
      const user = this.lobbyUsers[userId];
      html += `
        <div class="lobby-user" style="display: flex; align-items: center; margin: 5px 0;">
          <div style="width: 12px; height: 12px; border-radius: 50%; background: ${user.userColor}; margin-right: 8px;"></div>
          <span>${user.username}</span>
        </div>
      `;
    }
    lobbyPanel.innerHTML = html;
  }

  updateUserStatusDisplay() {
    // Update the user status display in the header
    const statusDisplay = document.getElementById('userStatus');
    if (statusDisplay) {
      statusDisplay.innerHTML = `
        <span style="color: ${this.myCursorColor};">●</span> ${this.userName}
        ${this.isMultiplayerMode ? `(Lobby: ${this.currentLobbyId})` : '(Single Player)'}
      `;
    }
  }

  handleIncomingStoryLoad(data) {
    // Handle incoming story load from another user
    // data: { storyJSON, initiatorId, storyName, storyId }

    // Add to pending sub-graphs
    this.pendingSubGraphs.push(data);

    // Show sub-graph overlay
    this.showSubGraphOverlay(data);
  }

  showSubGraphOverlay(storyData) {
    // Create overlay modal for story preview
    const overlay = document.createElement('div');
    overlay.className = 'subgraph-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 9999;
      display: flex;
      justify-content: center;
      align-items: center;
    `;

    const modal = document.createElement('div');
    modal.style.cssText = `
      background: #2a2a2a;
      border-radius: 8px;
      padding: 20px;
      max-width: 600px;
      max-height: 80%;
      overflow-y: auto;
      color: white;
    `;

    const initiatorName = this.lobbyUsers[storyData.initiatorId]?.username || 'Unknown User';

    modal.innerHTML = `
      <h3>Story from ${initiatorName}: ${storyData.storyName}</h3>
      <p>Nodes: ${storyData.storyJSON.nodes?.length || 0}, Connections: ${storyData.storyJSON.connections?.length || 0}</p>
      <div style="margin: 20px 0;">
        <button id="mergeStory" class="btn btn-primary" style="margin-right: 10px;">Merge into Main Canvas</button>
        <button id="dismissStory" class="btn btn-secondary">Dismiss</button>
      </div>
    `;

    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // Add event listeners
    document.getElementById('mergeStory').onclick = () => {
      this.mergeSubGraph(storyData);
      document.body.removeChild(overlay);
    };

    document.getElementById('dismissStory').onclick = () => {
      document.body.removeChild(overlay);
    };
  }

  mergeSubGraph(storyData) {
    // Merge the sub-graph into the main canvas
    const storyJSON = storyData.storyJSON;

    if (storyJSON.nodes) {
      // Add nodes with new IDs to avoid conflicts
      const idMapping = {};
      storyJSON.nodes.forEach(node => {
        const newId = this.generateUniqueNodeId();
        idMapping[node.id] = newId;

        const newNode = { ...node, id: newId };
        this.nodes.push(newNode);
      });

      // Add connections with updated IDs
      if (storyJSON.connections) {
        storyJSON.connections.forEach(conn => {
          if (idMapping[conn.from] && idMapping[conn.to]) {
            this.connections.push({
              ...conn,
              from: idMapping[conn.from],
              to: idMapping[conn.to]
            });
          }
        });
      }
    }

    // Update UI
    this.markUnsaved();
    this.recordHistory();
    this.updatePropertiesPanel();
    this.updateStats();
    this.render();

    // Broadcast the merged state if in multiplayer
    if (this.socket && this.isMultiplayerMode) {
      this.socket.emit('story-update', {
        nodes: this.nodes,
        connections: this.connections,
        groups: this.storyGroups || []
      });
    }

    this.showToast(`Merged story "${storyData.storyName}"`, 'success');
  }

  toggleMultiplayerMode() {
    this.isMultiplayerMode = !this.isMultiplayerMode;

    if (this.isMultiplayerMode) {
      // Show username/color selection modal if not set
      if (!this.userName || this.userName === 'Anonymous') {
        this.showUserInfoModal();
      } else {
        // Send existing user info to server
        this.socket.emit('set-user-info', {
          username: this.userName,
          userColor: this.myCursorColor,
          sessionToken: this.sessionToken
        });
      }
    }

    this.updateUserStatusDisplay();
    this.updateModeUI();
  }

  showUserInfoModal() {
    // Create modal for username and color selection
    const modal = document.createElement('div');
    modal.className = 'user-info-modal';
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      z-index: 10000;
      display: flex;
      justify-content: center;
      align-items: center;
    `;

    const form = document.createElement('div');
    form.style.cssText = `
      background: #2a2a2a;
      border-radius: 8px;
      padding: 30px;
      color: white;
      min-width: 300px;
    `;

    form.innerHTML = `
      <h3>Join Multiplayer Session</h3>
      <div style="margin: 20px 0;">
        <label>Username:</label>
        <input type="text" id="usernameInput" value="${this.userName || ''}" style="width: 100%; padding: 8px; margin-top: 5px; border-radius: 4px; border: none;">
      </div>
      <div style="margin: 20px 0;">
        <label>Color:</label>
        <input type="color" id="colorInput" value="${this.myCursorColor || '#00ff00'}" style="width: 100%; padding: 8px; margin-top: 5px; border-radius: 4px; border: none;">
      </div>
      <div style="margin: 20px 0;">
        <button id="joinBtn" class="btn btn-primary" style="width: 100%;">Join</button>
      </div>
    `;

    modal.appendChild(form);
    document.body.appendChild(modal);

    // Focus username input
    const usernameInput = document.getElementById('usernameInput');
    usernameInput.focus();

    // Add event listeners
    const joinBtn = document.getElementById('joinBtn');
    const colorInput = document.getElementById('colorInput');

    const handleJoin = () => {
      const username = usernameInput.value.trim();
      const color = colorInput.value;

      if (!username) {
        alert('Please enter a username');
        return;
      }

      // Send user info to server
      this.socket.emit('set-user-info', {
        username: username,
        userColor: color,
        sessionToken: this.sessionToken
      });

      document.body.removeChild(modal);
    };

    joinBtn.onclick = handleJoin;
    usernameInput.onkeypress = (e) => {
      if (e.key === 'Enter') handleJoin();
    };
  }

  updateModeUI() {
    // Update UI elements based on current mode
    const modeToggle = document.getElementById('multiplayerToggle');
    if (modeToggle) {
      modeToggle.textContent = this.isMultiplayerMode ? '🌐 Single Player' : '🌐 Multiplayer';
      modeToggle.className = this.isMultiplayerMode ? 'toggle-btn active' : 'toggle-btn';
    }

    // Show/hide lobby panel
    const lobbyPanel = document.getElementById('lobbyPanel');
    if (lobbyPanel) {
      lobbyPanel.style.display = this.isMultiplayerMode ? 'block' : 'none';
    }
  }

}

// ──────────────────────────────────────────────────────────────────────────────
//  OPEN USER SETTINGS MODAL
// ──────────────────────────────────────────────────────────────────────────────
function openUserSettings() {
  // Fill in the inputs with the current stored values:
  const sb = window.storyBuilderInstance; // assume you store your StoryBuilder in a global variable
  document.getElementById('userNameInput').value = sb.userName || '';
  document.getElementById('userColorInput').value = sb.myCursorColor || '#00ff00';

  // Show the modal
  document.getElementById('userSettingsModal').style.display = 'block';
}

// ──────────────────────────────────────────────────────────────────────────────
//  CLOSE USER SETTINGS MODAL
// ──────────────────────────────────────────────────────────────────────────────
function closeUserSettings() {
  document.getElementById('userSettingsModal').style.display = 'none';
}

// ──────────────────────────────────────────────────────────────────────────────
//  SAVE USER SETTINGS (name & color)
// ──────────────────────────────────────────────────────────────────────────────
function saveUserSettings() {
  const nameInput  = document.getElementById('userNameInput').value.trim();
  const colorInput = document.getElementById('userColorInput').value;

  // Update our StoryBuilder instance:
  const sb = window.storyBuilderInstance;
  sb.userName      = nameInput.length ? nameInput : 'Anonymous';
  sb.myCursorColor = colorInput;

  // Immediately send a “user-update” so others know we changed name/color:
  sb.socket.emit('user-update', {
    name: sb.userName,
    color: sb.myCursorColor
  });

  // Close the modal
  closeUserSettings();
}


        // Modal management functions
        function closeSkillModal() { 
            if (storyBuilder) storyBuilder.closeSkillModal();
        }
        
        function closeRequirementsModal() { 
            if (storyBuilder) storyBuilder.closeRequirementsModal();
        }
        
        function closeMetadataModal() { 
            if (storyBuilder) storyBuilder.closeMetadataModal();
        }
        
        function closeStoryBrowser() { 
            document.getElementById('storyBrowserModal').style.display = 'none'; 
        }

        // Main global storyBuilder instance
        let storyBuilder;
        
        function initializeStoryBuilder() {
        storyBuilder = new StoryBuilder();
        window.storyBuilderInstance = storyBuilder;
        // Immediately after creating it, force initial canvas setup & render:
        storyBuilder.setupCanvas();
        storyBuilder.render();
      }
        
        function newStory() { 
            if (confirm('Are you sure? This will clear the current story.')) {
                storyBuilder.clear(); 
            }
        }
        
        function saveStory() { 
            storyBuilder.saveStory(); 
        }
        
        function loadStory() { 
            storyBuilder.loadStory(); 
        }
        
        function clearCanvas() { 
            if (confirm('Are you sure? This will delete all nodes and connections.')) {
                storyBuilder.clear(); 
            }
        }
        
        function refreshStoryList() {
            storyBuilder.refreshStoryList();
        }
        
        function openStoryBrowser() {
            storyBuilder.openStoryBrowser();
        }

        function toggleMultiplayer() {
            if (storyBuilder) {
                storyBuilder.toggleMultiplayerMode();
            }
        }
        
        function exportStoryData() {
            const storyData = {
              version: "1.4",
              title: storyBuilder.currentStoryName || "Operation Homecoming Story",
              created: new Date().toISOString(),
              nodes: storyBuilder.nodes,
              connections: storyBuilder.connections,
              groups: storyBuilder.storyGroups || [],
              skillSystem: {
                availableSkills: RIMWORLD_SKILLS,
                competencyLevels: SKILL_COMPETENCY
              },
              factionSystem: {
                availableFactions: FACTIONS
              },
              traitSystem: {
                availableTraits: TRAITS
              },
              analytics: {
                skillUsage: storyBuilder.analyzeSkillUsage(),
                outcomeProbabilities: storyBuilder.analyzeOutcomeProbabilities()
              }
            };

            const jsonOutput = document.getElementById('jsonOutput');
            jsonOutput.textContent = JSON.stringify(storyData, null, 2);

            // Copy to clipboard
            navigator.clipboard.writeText(JSON.stringify(storyData, null, 2)).then(() => {
              console.log('Story data copied to clipboard');
            }).catch(err => {
              console.error('Failed to copy to clipboard:', err);
            });
          }

          
          // ────────────────────────────────────────────────────────────────────────────
      // 3) Now wire up the “DOMContentLoaded” listener, debug‐log clicks, etc.
      // ────────────────────────────────────────────────────────────────────────────
      document.addEventListener('DOMContentLoaded', () => {
        // 3a) First, build the StoryBuilder instance (must happen before you use storyBuilder).
        initializeStoryBuilder();
        // 3b) DEBUG: confirm that initializeStoryBuilder() succeeded:
        console.log('[DEBUG] StoryBuilder initialized:', !!storyBuilder);

        // 3c) Initialize multiplayer mode UI and show user info modal
        storyBuilder.updateModeUI();
        if (storyBuilder.isMultiplayerMode && (!storyBuilder.userName || storyBuilder.userName === 'Anonymous')) {
          setTimeout(() => storyBuilder.showUserInfoModal(), 500); // Small delay to ensure DOM is ready
        }

        // 3c) Attach click listeners to each “.node-type” in the sidebar:
        document.querySelectorAll('.node-type').forEach((buttonEl) => {
          // Click handler - create node at canvas center
          buttonEl.addEventListener('click', (e) => {
            // Only handle click if not dragging
            if (!storyBuilder.isDraggingNewNode) {
              const type = buttonEl.getAttribute('data-type');
              console.log('🛠️ [DEBUG] Sidebar node clicked:', type);

              // Compute canvas center in world coordinates:
              const canvas = storyBuilder.canvas;
              const { offsetX, offsetY, scale } = storyBuilder;
              const screenCenterX = canvas.width / 2;
              const screenCenterY = canvas.height / 2;
              const worldCenterX = (screenCenterX - offsetX) / scale;
              const worldCenterY = (screenCenterY - offsetY) / scale;

              // Finally, add the new node:
              storyBuilder.addNode(type, worldCenterX, worldCenterY);
            }
          });

          // Drag start handler - initiate ghost node creation
          buttonEl.addEventListener('mousedown', (e) => {
            if (e.button === 0) { // Left mouse button
              const type = buttonEl.getAttribute('data-type');
              storyBuilder.startNodeDrag(type, e);
              e.preventDefault(); // Prevent text selection
            }
          });
        });

        // 3d) OPTIONAL: initialize your multiplayer socket connection
        //     (you can now safely call io() since the Socket.IO client is loaded above)
        const socket = io(); // ← This will no longer throw “io is not defined”
        socket.on('connect', () => {
          console.log('[DEBUG] Connected to server via Socket.IO:', socket.id);
        });

        // Example: when the local story changes, broadcast it:
        socket.on('full-state', (data) => {
          console.log('[DEBUG] Received full-state from server:', data);
          // You could overwrite your local storyBuilder.nodes/connections here and re-render…
        });

        // Whenever you add or delete or move a node, emit “story-update”:
        // (Hook into your StoryBuilder’s code wherever you mutate `nodes` or `connections`)
        // e.g. each time you call storyBuilder.addNode(...), do:
        //     socket.emit('story-update', { nodes: storyBuilder.nodes, connections: storyBuilder.connections });
        //
        // And listen for inbound updates:
        socket.on('story-update', (data) => {
          console.log('[DEBUG] Another client updated the story:', data);
          // Here, you’d set storyBuilder.nodes = data.nodes, storyBuilder.connections = data.connections,
          // then call storyBuilder.render(), update stats, etc.
        });

        // (Similarly, if you want to share cursor positions, you’d do something like: )
        document.addEventListener('mousemove', (e) => {
          const rect = storyBuilder.canvas.getBoundingClientRect();
          const x = (e.clientX - rect.left - storyBuilder.offsetX) / storyBuilder.scale;
          const y = (e.clientY - rect.top  - storyBuilder.offsetY) / storyBuilder.scale;
          // e.g. socket.emit('cursor-move', { x, y, color: '#ff0000' });
        });
        socket.on('cursor-move', (data) => {
          // data = { sid: 'abc123', x: 123, y: 456, color: '#ff0000' }
          // Draw the other client’s cursor on your canvas, etc.
        });
      });

      // ────────────────────────────────────────────────────────────────────────────
      // 4) Global modal‐close on outside click
      // ────────────────────────────────────────────────────────────────────────────
      window.onclick = function(event) {
        ['requirementsModal', 'metadataModal', 'storyBrowserModal'].forEach(id => {
          const modal = document.getElementById(id);
          if (event.target === modal) modal.style.display = "none";
        });
      };

      // ────────────────────────────────────────────────────────────────────────────
      // 5) Cleanup on page unload (clear autosave interval)
      // ────────────────────────────────────────────────────────────────────────────
      window.addEventListener('beforeunload', () => {
        if (storyBuilder && storyBuilder.autosaveInterval) {
          clearInterval(storyBuilder.autosaveInterval);
        }
      });
    </script>
  </body>
</html>