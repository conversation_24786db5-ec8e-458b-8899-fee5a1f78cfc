# server_enhanced.py - Enhanced multiplayer server with node selection synchronization

# ─────────────────────────────────────────────────────────────────
#  0) Perform eventlet.monkey_patch() immediately, before any other imports
# ─────────────────────────────────────────────────────────────────
import eventlet
eventlet.monkey_patch()

# ─────────────────────────────────────────────────────────────────
#  1) Now import Flask and Flask-SocketIO (after monkey‐patch)
# ─────────────────────────────────────────────────────────────────
from flask import Flask, send_from_directory, request
from flask_socketio import SocketIO, emit, join_room, leave_room
import uuid
import time
import json

app = Flask(__name__, static_folder='.')
socketio = SocketIO(app, cors_allowed_origins="*")

# ─────────────────────────────────────────────────────────────────
#  2) Enhanced multiplayer state management
# ─────────────────────────────────────────────────────────────────
master_state = {
    "nodes": [],
    "connections": []
}

# Tracks each connected client's latest cursor position & user info:
# { sid(str): { x: float or None, y: float or None, color: str, name: str, sessionToken: str } }
client_cursors = {}

# Tracks node selections per user: { sid(str): nodeID or None }
user_selections = {}

# Tracks lobbies: { lobbyID(str): { users: {sid: userInfo}, storyHistory: [], currentNodes: [], currentConnections: [], currentGroups: [] } }
lobbies = {
    'default': {
        'users': {},
        'storyHistory': [],
        'currentNodes': [],
        'currentConnections': [],
        'currentGroups': []
    }
}

# Session persistence: { sessionToken(str): { username: str, userColor: str, lastSeen: timestamp } }
session_storage = {}

# User-to-lobby mapping: { sid(str): lobbyID(str) }
user_lobby_map = {}

# ─── NEW: Enhanced multiplayer state ─────────────────────────────
# Node ID counter for server-assigned unique IDs
next_node_id = 1

# Box selection tracking: { sid(str): { x1, y1, x2, y2, userColor } }
active_box_selections = {}

# ─────────────────────────────────────────────────────────────────
#  3) HTTP routes serve the HTML + static files
# ─────────────────────────────────────────────────────────────────
@app.route('/')
def index():
    return send_from_directory('.', 'operation-homecoming-story-Mobile.html')

@app.route('/<path:filename>')
def static_files(filename):
    return send_from_directory('.', filename)

# ─────────────────────────────────────────────────────────────────
#  4) Helper functions for multiplayer management
# ─────────────────────────────────────────────────────────────────

def generate_session_token():
    """Generate a unique session token for user persistence."""
    return str(uuid.uuid4())

def get_user_lobby(sid):
    """Get the lobby ID for a given user session ID."""
    return user_lobby_map.get(sid, 'default')

def get_lobby_users(lobby_id):
    """Get all users in a specific lobby."""
    return lobbies.get(lobby_id, {}).get('users', {})

def broadcast_to_lobby(lobby_id, event, data, exclude_sid=None):
    """Broadcast an event to all users in a specific lobby."""
    lobby_users = get_lobby_users(lobby_id)
    for sid in lobby_users:
        if sid != exclude_sid:
            emit(event, data, room=sid)

def cleanup_user_state(sid):
    """Clean up all state associated with a disconnected user."""
    # Remove from selections
    user_selections.pop(sid, None)
    
    # Remove from cursors
    client_cursors.pop(sid, None)
    
    # Remove from lobby
    lobby_id = user_lobby_map.pop(sid, None)
    if lobby_id and lobby_id in lobbies:
        lobbies[lobby_id]['users'].pop(sid, None)
    
    return lobby_id

# ─────────────────────────────────────────────────────────────────
#  5) WebSocket event handlers
# ─────────────────────────────────────────────────────────────────

@socketio.on('connect')
def handle_connect():
    sid = request.sid
    print(f"[{sid}] Connected.")
    
    # Add user to default lobby initially
    lobby_id = 'default'
    user_lobby_map[sid] = lobby_id
    join_room(lobby_id)
    
    # Initialize user state
    user_selections[sid] = None
    
    # Send the full "master" story state to the newly connected client
    lobby_state = {
        'nodes': lobbies[lobby_id]['currentNodes'],
        'connections': lobbies[lobby_id]['currentConnections'],
        'groups': lobbies[lobby_id]['currentGroups']
    }
    emit('full-state', lobby_state)
    
    # Send current lobby state
    lobby_users = get_lobby_users(lobby_id)
    emit('lobby-state', {
        'lobbyId': lobby_id,
        'users': lobby_users,
        'userSelections': {uid: user_selections.get(uid) for uid in lobby_users}
    })

@socketio.on('disconnect')
def handle_disconnect():
    sid = request.sid
    print(f"[{sid}] Disconnected.")
    
    # Get lobby before cleanup
    lobby_id = get_user_lobby(sid)
    
    # Clean up all user state
    cleanup_user_state(sid)
    
    # Notify lobby members that user left
    if lobby_id:
        broadcast_to_lobby(lobby_id, 'user-left', {'sid': sid}, exclude_sid=sid)
    
    # Notify all clients to remove cursor
    emit('cursor-remove', {'sid': sid}, broadcast=True)

# ─────────────────────────────────────────────────────────────────
#  6) Node Selection Events
# ─────────────────────────────────────────────────────────────────

@socketio.on('select-node')
def handle_select_node(data):
    """
    Handle node selection from a client.
    data: { nodeId: int, userColor: str }
    """
    sid = request.sid
    node_id = data.get('nodeId')
    user_color = data.get('userColor', '#00ff00')
    lobby_id = get_user_lobby(sid)
    
    # Clear previous selection for this user
    previous_selection = user_selections.get(sid)
    if previous_selection is not None:
        broadcast_to_lobby(lobby_id, 'node-deselected', {
            'nodeId': previous_selection,
            'userId': sid
        })
    
    # Update user's selection
    user_selections[sid] = node_id
    
    # Broadcast new selection to lobby
    broadcast_to_lobby(lobby_id, 'node-selected', {
        'nodeId': node_id,
        'userId': sid,
        'userColor': user_color
    })
    
    print(f"[{sid}] Selected node {node_id}")

@socketio.on('deselect-node')
def handle_deselect_node(data):
    """
    Handle node deselection from a client.
    """
    sid = request.sid
    lobby_id = get_user_lobby(sid)
    
    # Get current selection
    current_selection = user_selections.get(sid)
    if current_selection is not None:
        # Clear selection
        user_selections[sid] = None
        
        # Broadcast deselection to lobby
        broadcast_to_lobby(lobby_id, 'node-deselected', {
            'nodeId': current_selection,
            'userId': sid
        })
        
        print(f"[{sid}] Deselected node {current_selection}")

# ─────────────────────────────────────────────────────────────────
#  7) User Management Events
# ─────────────────────────────────────────────────────────────────

@socketio.on('set-user-info')
def handle_set_user_info(data):
    """
    Set user information (username, color, session token).
    data: { username: str, userColor: str, sessionToken: str (optional) }
    """
    sid = request.sid
    username = data.get('username', 'Anonymous')
    user_color = data.get('userColor', '#00ff00')
    session_token = data.get('sessionToken') or generate_session_token()
    lobby_id = get_user_lobby(sid)
    
    # Store session info
    session_storage[session_token] = {
        'username': username,
        'userColor': user_color,
        'lastSeen': time.time()
    }
    
    # Update client cursor info
    client_cursors[sid] = {
        'x': client_cursors.get(sid, {}).get('x'),
        'y': client_cursors.get(sid, {}).get('y'),
        'color': user_color,
        'name': username,
        'sessionToken': session_token
    }
    
    # Update lobby user info
    lobbies[lobby_id]['users'][sid] = {
        'username': username,
        'userColor': user_color,
        'sessionToken': session_token
    }
    
    # Confirm to client
    emit('user-info-confirmed', {
        'username': username,
        'userColor': user_color,
        'sessionToken': session_token
    })
    
    # Broadcast user joined to lobby
    broadcast_to_lobby(lobby_id, 'user-joined', {
        'userId': sid,
        'username': username,
        'userColor': user_color
    }, exclude_sid=sid)
    
    print(f"[{sid}] Set user info: {username} ({user_color})")

@socketio.on('reconnect-request')
def handle_reconnect_request(data):
    """
    Handle reconnection with session token.
    data: { sessionToken: str }
    """
    sid = request.sid
    session_token = data.get('sessionToken')
    
    if session_token and session_token in session_storage:
        session_info = session_storage[session_token]
        
        # Restore user info
        emit('user-info-restored', {
            'username': session_info['username'],
            'userColor': session_info['userColor'],
            'sessionToken': session_token
        })
        
        print(f"[{sid}] Restored session for {session_info['username']}")
    else:
        # Session not found
        emit('error', {
            'code': 'SESSION_NOT_FOUND',
            'message': 'Session not found. Please re-enter your information.'
        })

# ─────────────────────────────────────────────────────────────────
#  8) Story Management Events
# ─────────────────────────────────────────────────────────────────

@socketio.on('story-update')
def handle_story_update(data):
    """
    Received from a client whenever they mutate the story (nodes/connections/groups).
    Update the lobby state and broadcast the new state to everyone else.
    """
    sid = request.sid
    lobby_id = get_user_lobby(sid)

    # Update lobby state
    lobbies[lobby_id]['currentNodes'] = data.get('nodes', [])
    lobbies[lobby_id]['currentConnections'] = data.get('connections', [])
    lobbies[lobby_id]['currentGroups'] = data.get('groups', [])

    # Broadcast the updated state to all other clients in the lobby
    broadcast_to_lobby(lobby_id, 'story-update', data)

@socketio.on('story-load-requested')
def handle_story_load_requested(data):
    """
    Handle request to load a story in multiplayer mode.
    data: { storyJSON: object, initiatorId: str, storyName: str }
    """
    sid = request.sid
    lobby_id = get_user_lobby(sid)
    story_json = data.get('storyJSON', {})
    story_name = data.get('storyName', 'Untitled Story')

    # Add to lobby story history
    story_entry = {
        'storyId': str(uuid.uuid4()),
        'storyJSON': story_json,
        'initiatorId': sid,
        'storyName': story_name,
        'timestamp': time.time()
    }

    lobbies[lobby_id]['storyHistory'].append(story_entry)

    # Broadcast to all lobby members
    broadcast_to_lobby(lobby_id, 'story-loaded', {
        'storyJSON': story_json,
        'initiatorId': sid,
        'storyName': story_name,
        'storyId': story_entry['storyId']
    })

    print(f"[{sid}] Loaded story '{story_name}' in lobby {lobby_id}")

@socketio.on('save-story-to-server')
def handle_save_story_to_server(data):
    """
    Save a story to the server for the current lobby.
    data: { storyJSON: object, storyName: str }
    """
    sid = request.sid
    lobby_id = get_user_lobby(sid)
    story_json = data.get('storyJSON', {})
    story_name = data.get('storyName', 'Untitled Story')

    # Save to lobby's story history
    story_entry = {
        'storyId': str(uuid.uuid4()),
        'storyJSON': story_json,
        'initiatorId': sid,
        'storyName': story_name,
        'timestamp': time.time(),
        'saved': True
    }

    lobbies[lobby_id]['storyHistory'].append(story_entry)

    # Confirm save to client
    emit('story-saved-to-server', {
        'storyId': story_entry['storyId'],
        'storyName': story_name
    })

    print(f"[{sid}] Saved story '{story_name}' to server")

# ─────────────────────────────────────────────────────────────────
#  9) Cursor Movement Events (from original server)
# ─────────────────────────────────────────────────────────────────

@socketio.on('cursor-move')
def handle_cursor_move(data):
    """
    Received whenever a client moves their mouse over the canvas OR updates
    their name/color (because we piggyback name/color onto every cursor-move).
    `data` should contain at least: { "x": float, "y": float, "color": str, "name": str }
    """
    sid = request.sid
    # Update this client's stored cursor position & user info.
    client_cursors[sid] = {
        'x': data.get('x'),
        'y': data.get('y'),
        'color': data.get('color'),
        'name': data.get('name')
    }
    # Broadcast to all other clients so they can render this cursor + label
    emit('cursor-move',
         {
             'sid': sid,
             'x': data.get('x'),
             'y': data.get('y'),
             'color': data.get('color'),
             'name': data.get('name')
         },
         broadcast=True, include_self=False)

@socketio.on('user-join')
def handle_user_join(data):
    """
    Received once when a client first connects and announces its name/color.
    `data` contains: { "name": str, "color": str }
    We store that info (with x/y = None initially) and broadcast so others
    can learn this user's display name and cursor color immediately.
    """
    sid = request.sid
    name = data.get('name', 'Anonymous')
    color = data.get('color', '#00ff00')
    # Initialize this client's cursor info (x,y unknown yet)
    client_cursors[sid] = {
        'x': None,
        'y': None,
        'color': color,
        'name': name
    }
    # Broadcast a "cursor-move" with no position but with name/color
    # so other clients can create an entry for this SID.
    emit('cursor-move',
         {
             'sid': sid,
             'x': None,
             'y': None,
             'color': color,
             'name': name
         },
         broadcast=True, include_self=False)

@socketio.on('user-update')
def handle_user_update(data):
    """
    Received whenever a client changes their name or cursor color.
    `data` contains: { "name": str, "color": str }
    We update stored info and broadcast the new name/color to everyone else.
    """
    sid = request.sid
    name = data.get('name', 'Anonymous')
    color = data.get('color', '#00ff00')

    # Update stored info; retain existing x/y if present
    existing = client_cursors.get(sid, {})
    client_cursors[sid] = {
        'x': existing.get('x'),
        'y': existing.get('y'),
        'color': color,
        'name': name
    }

    # Broadcast a "cursor-move" with no position but with updated name/color
    emit('cursor-move',
         {
             'sid': sid,
             'x': None,
             'y': None,
             'color': color,
             'name': name
         },
         broadcast=True, include_self=False)

# ─────────────────────────────────────────────────────────────────
#  10) Error Handling
# ─────────────────────────────────────────────────────────────────

# ─────────────────────────────────────────────────────────────────
#  11) Enhanced Multiplayer Events
# ─────────────────────────────────────────────────────────────────

@socketio.on('request-new-node')
def handle_request_new_node(data):
    """
    Handle request for server-assigned node ID.
    data: { type: str, x: float, y: float, createdBy: str, title: str }
    """
    global next_node_id
    sid = request.sid

    # Generate unique node ID
    node_id = next_node_id
    next_node_id += 1

    # Broadcast node creation to all clients
    emit('node-created', {
        'nodeID': node_id,
        'type': data.get('type'),
        'x': data.get('x'),
        'y': data.get('y'),
        'createdBy': data.get('createdBy'),
        'title': data.get('title')
    }, broadcast=True)

    print(f"[{sid}] Created node {node_id} of type {data.get('type')}")

@socketio.on('remove-connection')
def handle_remove_connection(data):
    """
    Handle connection removal request.
    data: { from: int, to: int, removedBy: str }
    """
    sid = request.sid

    # Broadcast connection removal to all clients
    emit('connection-removed', {
        'from': data.get('from'),
        'to': data.get('to')
    }, broadcast=True)

    print(f"[{sid}] Removed connection from {data.get('from')} to {data.get('to')}")

@socketio.on('box-selecting')
def handle_box_selecting(data):
    """
    Handle box selection broadcasting.
    data: { x1: float, y1: float, x2: float, y2: float, userID: str, userColor: str }
    """
    sid = request.sid

    # Store active box selection
    active_box_selections[sid] = {
        'x1': data.get('x1'),
        'y1': data.get('y1'),
        'x2': data.get('x2'),
        'y2': data.get('y2'),
        'userColor': data.get('userColor')
    }

    # Broadcast to all other clients
    emit('box-selecting', data, broadcast=True, include_self=False)

@socketio.on('box-select-complete')
def handle_box_select_complete(data):
    """
    Handle box selection completion.
    data: { selectedNodeIDs: [int], userID: str }
    """
    sid = request.sid

    # Remove from active selections
    active_box_selections.pop(sid, None)

    # Broadcast completion to all other clients
    emit('box-select-complete', data, broadcast=True, include_self=False)

    print(f"[{sid}] Completed box selection with {len(data.get('selectedNodeIDs', []))} nodes")

@socketio.on('connection-label-changed')
def handle_connection_label_changed(data):
    """
    Handle connection label changes.
    data: { connectionID: {from: int, to: int}, newLabel: str, changedBy: str }
    """
    sid = request.sid

    # Broadcast label change to all other clients
    emit('connection-label-changed', data, broadcast=True, include_self=False)

    conn_id = data.get('connectionID', {})
    print(f"[{sid}] Changed connection label from {conn_id.get('from')} to {conn_id.get('to')}: {data.get('newLabel')}")

@socketio.on('group-created')
def handle_group_created(data):
    """
    Handle story group creation.
    data: { groupID: int, name: str, color: str, childNodeIDs: [int], createdBy: str }
    """
    sid = request.sid
    lobby_id = get_user_lobby(sid)

    # Broadcast group creation to all other clients in the lobby
    broadcast_to_lobby(lobby_id, 'group-created', data)

    print(f"[{sid}] Created story group '{data.get('name')}' with {len(data.get('childNodeIDs', []))} nodes")

@socketio.on('group-moved')
def handle_group_moved(data):
    """
    Handle story group movement.
    data: { groupID: int, deltaX: float, deltaY: float, newX: float, newY: float, movedBy: str }
    """
    sid = request.sid
    lobby_id = get_user_lobby(sid)

    # Broadcast group movement to all other clients in the lobby
    broadcast_to_lobby(lobby_id, 'group-moved', data)

    print(f"[{sid}] Moved story group {data.get('groupID')} to ({data.get('newX')}, {data.get('newY')})")

@socketio.on('group-updated')
def handle_group_updated(data):
    """
    Handle story group property updates.
    data: { groupID: int, property: str, value: any, updatedBy: str }
    """
    sid = request.sid
    lobby_id = get_user_lobby(sid)

    # Broadcast group update to all other clients in the lobby
    broadcast_to_lobby(lobby_id, 'group-updated', data)

    print(f"[{sid}] Updated story group {data.get('groupID')} property '{data.get('property')}' to '{data.get('value')}'")

@socketio.on('group-deleted')
def handle_group_deleted(data):
    """
    Handle story group deletion.
    data: { groupID: int, deletedBy: str }
    """
    sid = request.sid
    lobby_id = get_user_lobby(sid)

    # Broadcast group deletion to all other clients in the lobby
    broadcast_to_lobby(lobby_id, 'group-deleted', data)

    print(f"[{sid}] Deleted story group {data.get('groupID')}")

@socketio.on('error')
def handle_error(error):
    """Handle Socket.IO errors."""
    print(f"Socket.IO error: {error}")

if __name__ == '__main__':
    print("Starting enhanced multiplayer server on http://localhost:5000 ...")
    socketio.run(app, host='0.0.0.0', port=5000)
