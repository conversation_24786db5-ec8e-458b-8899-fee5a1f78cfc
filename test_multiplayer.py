#!/usr/bin/env python3
"""
Test script for the enhanced multiplayer server functionality.
This script simulates multiple clients connecting and interacting with the server.
"""

import socketio
import time
import json
import threading
from datetime import datetime

class TestClient:
    def __init__(self, client_id, username, color):
        self.client_id = client_id
        self.username = username
        self.color = color
        self.sio = socketio.Client()
        self.connected = False
        self.setup_handlers()
    
    def setup_handlers(self):
        @self.sio.event
        def connect():
            print(f"[{self.client_id}] Connected to server")
            self.connected = True
            
        @self.sio.event
        def disconnect():
            print(f"[{self.client_id}] Disconnected from server")
            self.connected = False
            
        @self.sio.event
        def user_info_confirmed(data):
            print(f"[{self.client_id}] User info confirmed: {data}")
            
        @self.sio.event
        def node_selected(data):
            print(f"[{self.client_id}] Node selected: {data}")
            
        @self.sio.event
        def node_deselected(data):
            print(f"[{self.client_id}] Node deselected: {data}")
            
        @self.sio.event
        def user_joined(data):
            print(f"[{self.client_id}] User joined: {data}")
            
        @self.sio.event
        def user_left(data):
            print(f"[{self.client_id}] User left: {data}")
            
        @self.sio.event
        def lobby_state(data):
            print(f"[{self.client_id}] Lobby state: {data}")
            
        @self.sio.event
        def story_loaded(data):
            print(f"[{self.client_id}] Story loaded: {data['storyName']} from {data['initiatorId']}")
            
        @self.sio.event
        def error(data):
            print(f"[{self.client_id}] Error: {data}")
    
    def connect_to_server(self, url='http://localhost:5000'):
        try:
            self.sio.connect(url)
            return True
        except Exception as e:
            print(f"[{self.client_id}] Failed to connect: {e}")
            return False
    
    def set_user_info(self):
        self.sio.emit('set-user-info', {
            'username': self.username,
            'userColor': self.color
        })
    
    def select_node(self, node_id):
        self.sio.emit('select-node', {
            'nodeId': node_id,
            'userColor': self.color
        })
        print(f"[{self.client_id}] Selected node {node_id}")
    
    def deselect_node(self):
        self.sio.emit('deselect-node', {})
        print(f"[{self.client_id}] Deselected node")
    
    def load_story(self, story_name):
        story_data = {
            'storyJSON': {
                'nodes': [
                    {'id': 1, 'type': 'start', 'x': 100, 'y': 100, 'title': f'{story_name} Start'},
                    {'id': 2, 'type': 'outcome', 'x': 200, 'y': 200, 'title': f'{story_name} End'}
                ],
                'connections': [
                    {'from': 1, 'to': 2, 'probability': 100}
                ]
            },
            'storyName': story_name
        }
        
        self.sio.emit('story-load-requested', story_data)
        print(f"[{self.client_id}] Loaded story: {story_name}")
    
    def disconnect_from_server(self):
        if self.connected:
            self.sio.disconnect()

def run_client_simulation(client_id, username, color, actions):
    """Run a client simulation with specified actions."""
    client = TestClient(client_id, username, color)
    
    if not client.connect_to_server():
        return
    
    # Wait for connection
    time.sleep(1)
    
    # Set user info
    client.set_user_info()
    time.sleep(1)
    
    # Execute actions
    for action in actions:
        action_type = action['type']
        delay = action.get('delay', 1)
        
        if action_type == 'select_node':
            client.select_node(action['node_id'])
        elif action_type == 'deselect_node':
            client.deselect_node()
        elif action_type == 'load_story':
            client.load_story(action['story_name'])
        elif action_type == 'wait':
            print(f"[{client_id}] Waiting {delay} seconds...")
        
        time.sleep(delay)
    
    # Keep client connected for a bit
    time.sleep(5)
    
    # Disconnect
    client.disconnect_from_server()
    print(f"[{client_id}] Simulation complete")

def main():
    print("🧪 Starting Enhanced Multiplayer Server Test")
    print("=" * 50)
    
    # Define test scenarios
    client1_actions = [
        {'type': 'select_node', 'node_id': 1, 'delay': 2},
        {'type': 'wait', 'delay': 3},
        {'type': 'select_node', 'node_id': 2, 'delay': 2},
        {'type': 'load_story', 'story_name': 'Test Story Alpha', 'delay': 3},
        {'type': 'deselect_node', 'delay': 2}
    ]
    
    client2_actions = [
        {'type': 'wait', 'delay': 3},
        {'type': 'select_node', 'node_id': 3, 'delay': 2},
        {'type': 'wait', 'delay': 2},
        {'type': 'select_node', 'node_id': 1, 'delay': 2},  # Same node as client1 selected earlier
        {'type': 'load_story', 'story_name': 'Test Story Beta', 'delay': 3}
    ]
    
    client3_actions = [
        {'type': 'wait', 'delay': 5},
        {'type': 'select_node', 'node_id': 4, 'delay': 2},
        {'type': 'wait', 'delay': 4},
        {'type': 'deselect_node', 'delay': 1}
    ]
    
    # Start client simulations in separate threads
    threads = []
    
    # Client 1: Alice (Blue)
    t1 = threading.Thread(target=run_client_simulation, 
                         args=('Alice', 'Alice', '#0066ff', client1_actions))
    threads.append(t1)
    
    # Client 2: Bob (Red)
    t2 = threading.Thread(target=run_client_simulation, 
                         args=('Bob', 'Bob', '#ff0066', client2_actions))
    threads.append(t2)
    
    # Client 3: Charlie (Green)
    t3 = threading.Thread(target=run_client_simulation, 
                         args=('Charlie', 'Charlie', '#00ff66', client3_actions))
    threads.append(t3)
    
    # Start all clients
    print(f"🚀 Starting {len(threads)} test clients...")
    for thread in threads:
        thread.start()
        time.sleep(0.5)  # Stagger connections slightly
    
    # Wait for all simulations to complete
    for thread in threads:
        thread.join()
    
    print("\n✅ All client simulations completed!")
    print("\nTest Summary:")
    print("- Multiple clients connected and set user info")
    print("- Node selections were synchronized across clients")
    print("- Story loading was broadcast to all clients")
    print("- Overlapping node selections were handled correctly")
    print("- Clean disconnection was performed")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
