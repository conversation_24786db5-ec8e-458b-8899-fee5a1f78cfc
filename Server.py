# server.py

# ─────────────────────────────────────────────────────────────────
#  0) Perform eventlet.monkey_patch() immediately, before any other imports
# ─────────────────────────────────────────────────────────────────
import eventlet
eventlet.monkey_patch()

# ─────────────────────────────────────────────────────────────────
#  1) Now import Flask and Flask-SocketIO (after monkey‐patch)
# ─────────────────────────────────────────────────────────────────
from flask import <PERSON><PERSON><PERSON>, send_from_directory, request
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room
import uuid
import time
import json

app = Flask(__name__, static_folder='.')
socketio = SocketIO(app, cors_allowed_origins="*")

# ─────────────────────────────────────────────────────────────────
#  2) In‐memory “master” state and enhanced multiplayer tracking
# ─────────────────────────────────────────────────────────────────
master_state = {
    "nodes": [],
    "connections": []
}

# Enhanced multiplayer state management
# Tracks each connected client’s latest cursor position & user info:
# { sid(str): { x: float or None, y: float or None, color: str, name: str, sessionToken: str } }
client_cursors = {}

# Tracks node selections per user: { sid(str): nodeID or None }
user_selections = {}

# Tracks lobbies: { lobbyID(str): { users: {sid: userInfo}, storyHistory: [], currentNodes: [], currentConnections: [] } }
lobbies = {
    'default': {
        'users': {},
        'storyHistory': [],
        'currentNodes': [],
        'currentConnections': []
    }
}

# Session persistence: { sessionToken(str): { username: str, userColor: str, lastSeen: timestamp } }
session_storage = {}

# User-to-lobby mapping: { sid(str): lobbyID(str) }
user_lobby_map = {}

# ─────────────────────────────────────────────────────────────────
#  3) HTTP routes serve the HTML + static files
# ─────────────────────────────────────────────────────────────────
@app.route('/')
def index():
    # Serve the main HTML for the story builder.
    # If your file has a different name, update it here.
    return send_from_directory('.', 'operation-homecoming-story-Mobile.html')

@app.route('/<path:filename>')
def static_files(filename):
    # Serve any supporting JS, CSS, images, etc.
    return send_from_directory('.', filename)

# ─────────────────────────────────────────────────────────────────
#  4) Helper functions for multiplayer management
# ─────────────────────────────────────────────────────────────────

def generate_session_token():
    """Generate a unique session token for user persistence."""
    return str(uuid.uuid4())

def get_user_lobby(sid):
    """Get the lobby ID for a given user session ID."""
    return user_lobby_map.get(sid, 'default')

def get_lobby_users(lobby_id):
    """Get all users in a specific lobby."""
    return lobbies.get(lobby_id, {}).get('users', {})

def broadcast_to_lobby(lobby_id, event, data, exclude_sid=None):
    """Broadcast an event to all users in a specific lobby."""
    lobby_users = get_lobby_users(lobby_id)
    for sid in lobby_users:
        if sid != exclude_sid:
            emit(event, data, room=sid)

def cleanup_user_state(sid):
    """Clean up all state associated with a disconnected user."""
    # Remove from selections
    user_selections.pop(sid, None)

    # Remove from cursors
    client_cursors.pop(sid, None)

    # Remove from lobby
    lobby_id = user_lobby_map.pop(sid, None)
    if lobby_id and lobby_id in lobbies:
        lobbies[lobby_id]['users'].pop(sid, None)

    return lobby_id

# ─────────────────────────────────────────────────────────────────
#  5) WebSocket event handlers
# ─────────────────────────────────────────────────────────────────

@socketio.on('connect')
def handle_connect():
    sid = request.sid
    print(f"[{sid}] Connected.")
    # Immediately send the full “master” story state to the newly connected client
    emit('full-state', master_state)
    # If this client has existing cursor info (unlikely on initial connect), send that too
    # But generally, other clients will broadcast their own “cursor-move” on join.

@socketio.on('disconnect')
def handle_disconnect():
    sid = request.sid
    print(f"[{sid}] Disconnected.")
    # Remove this client’s cursor/user info and notify all others to remove it
    client_cursors.pop(sid, None)
    emit('cursor-remove', {'sid': sid}, broadcast=True)

@socketio.on('story-update')
def handle_story_update(data):
    """
    Received from a client whenever they mutate the story (nodes/connections).
    Overwrite the master_state and broadcast the new state to everyone else.
    """
    global master_state
    # Expect data of form { "nodes": [...], "connections": [...] }
    master_state = data
    # Broadcast the updated state to all other clients
    emit('story-update', data, broadcast=True, include_self=False)

@socketio.on('cursor-move')
def handle_cursor_move(data):
    """
    Received whenever a client moves their mouse over the canvas OR updates
    their name/color (because we piggyback name/color onto every cursor-move).
    `data` should contain at least: { "x": float, "y": float, "color": str, "name": str }
    """
    sid = request.sid
    # Update this client’s stored cursor position & user info.
    client_cursors[sid] = {
        'x': data.get('x'),
        'y': data.get('y'),
        'color': data.get('color'),
        'name': data.get('name')
    }
    # Broadcast to all other clients so they can render this cursor + label
    emit('cursor-move',
         {
             'sid': sid,
             'x': data.get('x'),
             'y': data.get('y'),
             'color': data.get('color'),
             'name': data.get('name')
         },
         broadcast=True, include_self=False)

@socketio.on('user-join')
def handle_user_join(data):
    """
    Received once when a client first connects and announces its name/color.
    `data` contains: { "name": str, "color": str }
    We store that info (with x/y = None initially) and broadcast so others
    can learn this user’s display name and cursor color immediately.
    """
    sid = request.sid
    name = data.get('name', 'Anonymous')
    color = data.get('color', '#00ff00')
    # Initialize this client’s cursor info (x,y unknown yet)
    client_cursors[sid] = {
        'x': None,
        'y': None,
        'color': color,
        'name': name
    }
    # Broadcast a “cursor-move” with no position but with name/color
    # so other clients can create an entry for this SID.
    emit('cursor-move',
         {
             'sid': sid,
             'x': None,
             'y': None,
             'color': color,
             'name': name
         },
         broadcast=True, include_self=False)

@socketio.on('user-update')
def handle_user_update(data):
    """
    Received whenever a client changes their name or cursor color.
    `data` contains: { "name": str, "color": str }
    We update stored info and broadcast the new name/color to everyone else.
    """
    sid = request.sid
    name = data.get('name', 'Anonymous')
    color = data.get('color', '#00ff00')

    # Update stored info; retain existing x/y if present
    existing = client_cursors.get(sid, {})
    client_cursors[sid] = {
        'x': existing.get('x'),
        'y': existing.get('y'),
        'color': color,
        'name': name
    }

    # Broadcast a “cursor-move” with no position but with updated name/color
    emit('cursor-move',
         {
             'sid': sid,
             'x': None,
             'y': None,
             'color': color,
             'name': name
         },
         broadcast=True, include_self=False)

# ─────────────────────────────────────────────────────────────────
#  5) Launch the server
# ─────────────────────────────────────────────────────────────────
if __name__ == '__main__':
    print("Starting server on http://localhost:5000 ...")
    # Use eventlet’s WSGI server under the hood
    socketio.run(app, host='0.0.0.0', port=5000)
